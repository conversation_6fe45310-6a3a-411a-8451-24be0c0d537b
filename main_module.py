# 设置QT_QPA_PLATFORM_PLUGIN_PATH环境变量
import os
import sys

def resource_path(relative_path):
    """兼容 PyInstaller 单文件和开发环境的资源路径"""
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.abspath(os.path.dirname(__file__)), relative_path)

os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = resource_path('platforms')

# 导入必要的库
import os
import json
import websockets
import asyncio
import time
import random
import websocket
import traceback
import hmac
import hashlib
import base64
from websocket import create_connection
from PyQt5.QtWidgets import (
    QWidget, QApplication, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QTabWidget, QFormLayout, QCheckBox, QComboBox,
    QLineEdit, QSpinBox, QDoubleSpinBox, QDialog, QMessageBox,
    QSlider, QProgressDialog, QInputDialog, QSystemTrayIcon, QScrollArea,
    QGroupBox, QListWidget, QFileDialog, QProgressBar, QListWidgetItem,
    QColorDialog, QRadioButton
)
from PyQt5.QtCore import Qt, QTimer, QSettings, QThread, pyqtSignal, QUrl, QIODevice, QPoint
from PyQt5.QtGui import QPixmap, QIcon, QPainter, QPolygon, QBrush, QPen, QColor
from PyQt5.QtMultimedia import QMediaPlayer, QMediaContent, QAudioDeviceInfo, QAudio, QAudioOutput, QAudioFormat
import requests
import subprocess
import platform
try:
    import numpy as np
    import soundfile as sf
    AUDIO_PROCESSING_AVAILABLE = True
except ImportError:
    AUDIO_PROCESSING_AVAILABLE = False
    print("⚠️ 音频处理库未安装，增益和音调功能将不可用")
import re
from datetime import datetime, timedelta
import ctypes
import platform
import psutil
import win32gui
import win32process
import mmap
import win32api
import win32security
import tempfile
import wmi
from dotenv import load_dotenv
import webbrowser
import shutil
import subprocess

def get_machine_code():
    """
    获取设备的唯一机器码
    @return: str 机器码（64位十六进制字符串）
    """
    try:
        # print("开始获取机器码...")
        # 获取系统信息
        c = wmi.WMI()

        # 获取CPU信息
        # print("获取CPU信息...")
        cpu_info = c.Win32_Processor()[0]
        cpu_id = cpu_info.ProcessorId.strip()
        # print(f"CPU ID: {cpu_id}")

        # 获取主板序列号
        # print("获取主板信息...")
        board_info = c.Win32_BaseBoard()[0]
        board_id = board_info.SerialNumber.strip()
        # print(f"主板序列号: {board_id}")

        # 获取BIOS信息
        # print("获取BIOS信息...")
        bios_info = c.Win32_BIOS()[0]
        bios_id = bios_info.SerialNumber.strip()
        # print(f"BIOS序列号: {bios_id}")

        # 获取硬盘序列号
        # print("获取硬盘信息...")
        disk_info = c.Win32_DiskDrive()[0]
        disk_id = disk_info.SerialNumber.strip()
        # print(f"硬盘序列号: {disk_id}")

        # 组合所有硬件信息
        hardware_str = f"{cpu_id}:{board_id}:{bios_id}:{disk_id}"
        # print(f"组合的硬件信息: {hardware_str}")

        # 使用SHA256生成最终的机器码
        machine_code = hashlib.sha256(hardware_str.encode()).hexdigest()
        # print(f"生成的机器码: {machine_code}")
        return machine_code
    except Exception as e:
        print(f"获取机器码时出错: {e}")
        # 如果出错，生成一个基于当前用户名和计算机名的备用机器码
        try:
            # print("尝试使用备用方案（用户名+计算机名）...")
            username = win32api.GetUserName()
            computer_name = win32api.GetComputerName()
            backup_str = f"{username}:{computer_name}"
            # print(f"备用信息: {backup_str}")
            backup_code = hashlib.sha256(backup_str.encode()).hexdigest()
            # print(f"备用机器码: {backup_code}")
            return backup_code
        except Exception as backup_error:
            print(f"备用方案也失败了: {backup_error}")
            # 如果还是失败，返回一个基于当前时间的随机码
            # print("使用基于时间的随机码...")
            random_code = hashlib.sha256(str(time.time()).encode()).hexdigest()
            # print(f"随机机器码: {random_code}")
            return random_code

# 获取当前脚本所在目录的绝对路径
current_dir = resource_path('.')

# 设置 Qt 插件路径
os.environ['QT_PLUGIN_PATH'] = resource_path('platforms')

# 确保插件目录存在
qt_plugin_dir = resource_path('platforms')
if not os.path.exists(qt_plugin_dir):
    os.makedirs(qt_plugin_dir)

# 加载环境变量
load_dotenv()

# Gitee仓库配置
GITEE_TOKEN = "b995afca4f56b84ef2fedaf533e3ce9f"  # Gitee Token
REPO_OWNER = "chen-duohao"  # Gitee用户名
REPO_NAME = "activation_keys"  # 仓库名称
GENERATED_KEYS_FILE = "generated_keys1.json"  # 新的哈希激活码表

# --- 将常量移到全局作用域 --- #
KEY_PREFIX = "HaoHNB" # <-- 卡密前缀，要和 generate_key.py 一致
SETTINGS_ORG = "YourOrgName" # <-- 组织名称 (用于 QSettings)
SETTINGS_APP = "OBSController" # <-- 应用名称 (用于 QSettings)
# -------------------------- #

# 添加签名密钥（这个密钥需要和生成器保持一致）
SIGNATURE_KEY = "HaoHNBv2Secret20240301"  # 建议使用更复杂的密钥

# 定义当前版本号
VERSION = "1.3"  # 当前版本号，每次更新时需要修改

# --- 辅助函数：线性插值 --- (不再需要，移除或注释掉)
# def lerp(start, end, ratio):
#     """计算线性插值。"""
#     # 确保 ratio 在 0.0 到 1.0 之间
#     ratio = max(0.0, min(ratio, 1.0))
#     return start + (end - start) * ratio

# --- 主窗口类 ---
class MainWindow(QWidget):
    """
    应用程序的主窗口。

    Attributes:
        ws: WebSocket 连接对象。
        is_connected (bool): 当前是否已连接到 OBS。
    """
    def __init__(self, parent=None):
        """
        @param {QWidget} parent - 父窗口，默认为 None。
        """
        super().__init__(parent)

        # 初始化 QSettings
        self.settings = QSettings(SETTINGS_ORG, SETTINGS_APP)

        # print("正在初始化主窗口...")

        # 设置窗口图标
        self.setWindowIcon(QIcon('obs2.ico'))

        # 创建系统托盘图标
        self.tray_icon = QSystemTrayIcon(self)
        self.tray_icon.setIcon(QIcon('obs2.ico'))
        self.tray_icon.setToolTip('OBS Controller')
        self.tray_icon.show()
        
        # --- 先定义所有状态变量 --- 
        self.ws = None
        self.is_connected = False
        self.request_id_counter = 0
        self.pending_requests = {}
        self.media_sources = []
        self.connected_obs_info = {}
        
        # 初始化各模块的状态变量
        self.speed_control = {
            "enabled": False,
            "source_name": "",
            "min_speed": 90,
            "max_speed": 120,
            "timer": QTimer(self),
            "interval_ms": 500,
            "current_speed": 100,
            "last_media_cursor": 0.0,
            "last_check_time": 0.0,
            "stall_check_start_time": 0.0,
            "stall_threshold_secs": 1.5
        }
        
        self.blur_control = {
            "enabled": False,
            "source_name": "",
            "filter_name": "Composite Blur",
            "min_radius": 0.0,
            "max_radius": 2.0,
            "interval_ms": 1000,
            "timer": QTimer(self),
            "original_radius": 0.0
        }
        
        self.transform_control = {
            "enabled": False,
            "source_name": "",
            "fixed_scale": 110,
            "interval_secs": 2,
            "transition_secs": 10,
            "debug": True,
            "timer": QTimer(self),
            "base_pos_x": 0,
            "base_pos_y": 0,
            "current_pos_x": 0,
            "current_pos_y": 0,
            "target_pos_x": 0,
            "target_pos_y": 0,
            "safe_move_x": 0,
            "safe_move_y": 0,
            "is_animating": False,
            "animation_start_time": 0.0,
            "canvas_width": 1080,
            "canvas_height": 1920
        }
        
        self.color_control = {
            "enabled": False,
            "source_name": "",
            "filter_name": "自动颜色校正",
            "min_hue": -2.0, "max_hue": 10.0,
            "min_brightness": -0.05, "max_brightness": 0.05,
            "min_contrast": -0.2, "max_contrast": 0.2,
            "min_saturation": -0.2, "max_saturation": 0.2,
            "min_gamma": -0.3, "max_gamma": 0.3,
            "interval_ms": 1500,
            "timer": QTimer(self)
        }
        
        self.audio_eq_control = {
            "enabled": False,
            "source_name": "",
            "filter_name": "3段式均衡器",
            "min_low_gain": -2.0, "max_low_gain": 2.0,
            "min_mid_gain": -2.0, "max_mid_gain": 2.0,
            "min_high_gain": -2.0, "max_high_gain": 2.0,
            "interval_secs": 1.0,
            "timer": QTimer(self),
            "original_gains": {"low": 0.0, "mid": 0.0, "high": 0.0}
        }

        # 初始化反调试
        self.anti_debug = AntiDebug()
        
        # 启动定时器定期检查
        self.security_timer = QTimer()
        self.security_timer.timeout.connect(self.security_check)
        self.security_timer.start(1000)  # 每秒检查一次

        # 初始化完整性检查器
        self.integrity_checker = IntegrityChecker()
        
        # 启动完整性检查定时器
        self.integrity_timer = QTimer()
        self.integrity_timer.timeout.connect(self.check_integrity)
        self.integrity_timer.start(5000)  # 每5秒检查一次

        # --- 自动压缩控制状态 ---
        self.compressor_control = {
            "enabled": False,
            "source_name": "",
            "filter_name": "自动压缩控制",
            "min_ratio": 10.0, "max_ratio": 13.0,
            "min_threshold": -20.0, "max_threshold": -18.0,
            "min_output_gain": 0.0, "max_output_gain": 1.0,
            "min_release": 50, "max_release": 150,
            "interval_secs": 1.0,
            "timer": QTimer(self)
        }
        # --- 自动增益控制状态 ---
        self.gain_control = {
            "enabled": False,
            "source_name": "",
            "filter_name": "自动增益控制",
            "min_gain": -3.0, "max_gain": 3.0,  # 增益范围：-3dB到+3dB
            "interval_secs": 1.0,
            "timer": QTimer(self)
        }

        # --- 断音控制状态 ---
        self.audio_mute_control = {
            "enabled": False,
            "source_name": "",
            "filter_name": "断音控制",
            "min_interval_secs": 30.0, "max_interval_secs": 40.0,  # 断音间隔范围
            "min_mute_duration_secs": 0.1, "max_mute_duration_secs": 0.5,  # 断音持续时间范围
            "timer": QTimer(self),
            "mute_timer": QTimer(self),  # 用于控制断音持续时间
            "original_volume": 1.0,  # 保存原始音量
            "is_muted": False  # 当前是否处于断音状态
        }

        # --- 随机音频播放大小控制状态 ---
        self.audio_volume_control = {
            "enabled": False,
            "source_name": "",
            "volume_min_percent": 60,  # 音量最小值百分比（对应0.6倍数）
            "volume_max_percent": 80,  # 音量最大值百分比（对应0.8倍数）
            "min_interval_secs": 1.0, "max_interval_secs": 5.0,  # 间隔范围
            "timer": QTimer(self),
            "original_volume": 1.0,  # 保存原始音量
            "is_active": False  # 当前是否处于活动状态
        }

        # --- 插件去重控制状态 ---
        self.plugin_dedup_control = {
            "enabled": False,
            "source_name": "",
            "plugins": [
                {
                    "name": "camelCrusher",
                    "display_name": "CamelCrusher 失真效果",
                    "filter_name": "CamelCrusher_去重",
                    "plugin_path": r"C:\Program Files\VSTPlugins\camelCrusher.dll",
                    "filter_kind": "vst_filter",
                    "min_interval_secs": 10.0,
                    "max_interval_secs": 30.0,
                    "min_duration_secs": 5.0,
                    "max_duration_secs": 15.0,
                    "timer": QTimer(self),
                    "duration_timer": QTimer(self),
                    "is_active": False
                },
                {
                    "name": "TAL-Reverb-4-64",
                    "display_name": "TAL 混响效果",
                    "filter_name": "TAL_Reverb_去重",
                    "plugin_path": r"C:\Program Files\VSTPlugins\TAL-Reverb-4-64.dll",
                    "filter_kind": "vst_filter",
                    "min_interval_secs": 15.0,
                    "max_interval_secs": 40.0,
                    "min_duration_secs": 8.0,
                    "max_duration_secs": 20.0,
                    "timer": QTimer(self),
                    "duration_timer": QTimer(self),
                    "is_active": False
                },
                {
                    "name": "TSE_808_2.0_x64",
                    "display_name": "TSE808 失真效果",
                    "filter_name": "TSE808_去重",
                    "plugin_path": r"C:\Program Files\VSTPlugins\TSE_808_2.0_x64.dll",
                    "filter_kind": "vst_filter",
                    "min_interval_secs": 12.0,
                    "max_interval_secs": 35.0,
                    "min_duration_secs": 6.0,
                    "max_duration_secs": 18.0,
                    "timer": QTimer(self),
                    "duration_timer": QTimer(self),
                    "is_active": False
                }
            ]
        }

        # 初始化UI（这会创建所有UI组件）
        # print("初始化UI组件...")
        self.initUI()

        # 初始化定时器
        # print("设置定时器...")
        self.setup_timers()

        # 连接信号（UI组件已创建后才能连接）
        # print("连接信号...")
        self.connect_signals()

        # 初始化爆闪播放器
        # print("初始化爆闪播放器...")
        self.init_flash_player()

        # 加载设置
        print("🚀 准备加载设置...")
        self.load_settings()
        print("🚀 load_settings() 调用完成")

        # 检查更新
        # print("准备检查更新...")
        QTimer.singleShot(1000, self.check_for_updates)  # 延迟1秒后检查更新

        # 禁用功能直到激活
        # print("禁用功能等待激活...")
        self.disable_features()

        # print("主窗口初始化完成")

    def initUI(self):
        """
        初始化窗口界面元素 (使用 Tab 页)。
        """
        self.setWindowTitle('OBS 去重软件 v1.3 ') # 更新标题
        # --- 调整窗口大小以完整显示所有功能，特别是碎片数据范围 --- #
        self.setGeometry(200, 50, 1100, 1000) # 进一步增大宽度和高度
        # 设置最小窗口大小，确保内容不会被压缩
        self.setMinimumSize(1100, 1000)
        # ---------------------- #

        main_layout = QVBoxLayout()

        # --- 顶部连接区域 --- (保持不变)
        connection_layout = QHBoxLayout()
        self.status_label = QLabel('未连接')
        self.status_label.setAlignment(Qt.AlignCenter)
        self.connect_button = QPushButton('连接到 OBS')
        self.connect_button.clicked.connect(self.connect_to_obs)

        # --- 新增：一键启动按钮 ---
        self.quick_start_button = QPushButton('一键启动')
        self.quick_start_button.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
                color: white !important;
                border: 2px solid #059669 !important;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
                font-size: 11pt;
            }
            QPushButton:enabled {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
                color: white !important;
                border: 2px solid #059669 !important;
            }
            QPushButton:hover:enabled {
                background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
                border: 2px solid #047857 !important;
                transform: translateY(-1px);
            }
            QPushButton:pressed:enabled {
                background: linear-gradient(135deg, #047857 0%, #065f46 100%) !important;
                border: 2px solid #065f46 !important;
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background: #9ca3af !important;
                color: #6b7280 !important;
                border: 2px solid #9ca3af !important;
            }
        """)
        self.quick_start_button.clicked.connect(self.show_quick_start_dialog)
        self.quick_start_button.setEnabled(False)  # 初始禁用，连接后启用

        # 一键启动状态管理
        self.is_batch_running = False
        self.active_functions = []  # 记录当前活跃的功能

        # --- 音频播放器状态管理 ---
        self.media_player = QMediaPlayer()
        self.current_playlist = []  # 当前播放列表
        self.current_index = 0      # 当前播放索引
        self.is_playing = False     # 播放状态
        self.is_repeat = False      # 循环播放
        self.is_shuffle = False     # 随机播放
        self.volume = 50            # 音量 (0-100)

        # 音频输出设备管理
        self.current_audio_output = None
        self.selected_audio_device = None

        # --- 爆闪播放器状态管理 ---
        self.flash_player_state = {
            "colors": [],  # 存储添加的颜色
            "is_playing": False,  # 播放状态
            "play_mode": "顺序轮播",  # 播放模式：顺序轮播/随机乱序轮播
            "ignore_interval": False,  # 无视间隔设置
            "min_interval": 500,  # 最小间隔(ms)
            "max_interval": 2000,  # 最大间隔(ms)
            "fragment_min": 50,  # 碎片数据最小值
            "fragment_max": 100,  # 碎片数据最大值
            "current_color_index": 0,  # 当前颜色索引
            "flash_window": None,  # 播放窗口引用
            "flash_timer": QTimer(self)  # 播放定时器
        }

        connection_layout.addWidget(self.status_label, 1) # 让标签占据更多空间
        connection_layout.addWidget(self.connect_button)
        connection_layout.addWidget(self.quick_start_button)
        main_layout.addLayout(connection_layout)

        # --- 创建 Tab 控件 --- #
        self.tab_widget = QTabWidget()

        # --- Tab 1: 视频去重 --- #
        video_tab = QWidget()
        video_main_layout = QVBoxLayout(video_tab)

        # 视频去重标题和媒体源选择
        video_header_layout = QVBoxLayout()
        video_title = QLabel("🎬 视频去重功能")
        video_title.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #667eea;
                padding: 10px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        video_title.setAlignment(Qt.AlignCenter)
        video_header_layout.addWidget(video_title)

        # 统一的视频媒体源选择
        video_source_layout = QHBoxLayout()
        video_source_label = QLabel("视频媒体源:")
        video_source_label.setStyleSheet("font-weight: bold; color: #1e293b;")
        self.video_source_combo = QComboBox()
        self.video_source_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background: white;
                min-height: 20px;
            }
            QComboBox:focus {
                border-color: #667eea;
            }
        """)
        refresh_button_video = QPushButton("🔄 刷新媒体源")
        refresh_button_video.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: #808080;
                border: none;
                padding: 8px 16px;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
                transform: translateY(-1px);
            }
        """)
        refresh_button_video.clicked.connect(self.refresh_media_sources)
        video_source_layout.addWidget(video_source_label)
        video_source_layout.addWidget(self.video_source_combo, 1)
        video_source_layout.addWidget(refresh_button_video)
        video_header_layout.addLayout(video_source_layout)
        video_main_layout.addLayout(video_header_layout)

        # 连接视频媒体源选择信号
        self.video_source_combo.currentTextChanged.connect(self.update_video_source_for_all_functions)


        # 创建视频功能的Tab控件
        video_sub_tabs = QTabWidget()
        video_sub_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                background: white;
            }
            QTabBar::tab {
                background: #f1f5f9;
                color: #64748b;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
            QTabBar::tab:selected {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: black;
            }
            QTabBar::tab:hover {
                background: #e2e8f0;
            }
        """)

        # 子Tab 1: 智能加减速
        speed_tab = QWidget()
        speed_layout = QFormLayout(speed_tab)
        speed_layout.setSpacing(15)

        self.speed_checkbox = QCheckBox("启用智能加减速")
        self.speed_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #cbd5e1;
            }
            QCheckBox::indicator:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border-color: #059669;
            }
        """)
        self.speed_checkbox.stateChanged.connect(self.toggle_speed_control)
        speed_layout.addRow(self.speed_checkbox)

        min_speed_layout = QHBoxLayout()
        self.speed_min_slider = QSlider(Qt.Horizontal)
        self.speed_min_slider.setRange(1, 200)
        self.speed_min_slider.setValue(self.speed_control["min_speed"])
        self.speed_min_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #cbd5e1;
                height: 8px;
                background: #f1f5f9;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: 2px solid #667eea;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
        """)
        self.speed_min_spinbox = QSpinBox()
        self.speed_min_spinbox.setRange(1, 200)
        self.speed_min_spinbox.setValue(self.speed_control["min_speed"])
        self.speed_min_spinbox.setSuffix(" %")
        self.speed_min_slider.valueChanged.connect(self.speed_min_spinbox.setValue)
        self.speed_min_spinbox.valueChanged.connect(self.speed_min_slider.setValue)
        self.speed_min_spinbox.valueChanged.connect(lambda value: self.update_speed_setting('min_speed', value))
        min_speed_layout.addWidget(self.speed_min_slider)
        min_speed_layout.addWidget(self.speed_min_spinbox)
        speed_layout.addRow("最低速度:", min_speed_layout)

        max_speed_layout = QHBoxLayout()
        self.speed_max_slider = QSlider(Qt.Horizontal)
        self.speed_max_slider.setRange(1, 200)
        self.speed_max_slider.setValue(self.speed_control["max_speed"])
        self.speed_max_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #cbd5e1;
                height: 8px;
                background: #f1f5f9;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border: 2px solid #667eea;
                width: 18px;
                margin: -5px 0;
                border-radius: 9px;
            }
        """)
        self.speed_max_spinbox = QSpinBox()
        self.speed_max_spinbox.setRange(1, 200)
        self.speed_max_spinbox.setValue(self.speed_control["max_speed"])
        self.speed_max_spinbox.setSuffix(" %")
        self.speed_max_slider.valueChanged.connect(self.speed_max_spinbox.setValue)
        self.speed_max_spinbox.valueChanged.connect(self.speed_max_slider.setValue)
        self.speed_max_spinbox.valueChanged.connect(lambda value: self.update_speed_setting('max_speed', value))
        max_speed_layout.addWidget(self.speed_max_slider)
        max_speed_layout.addWidget(self.speed_max_spinbox)
        speed_layout.addRow("最高速度:", max_speed_layout)

        video_sub_tabs.addTab(speed_tab, "🚀 智能加减速")

        # 子Tab 2: 模糊去重
        blur_tab = QWidget()
        blur_layout = QFormLayout(blur_tab)
        blur_layout.setSpacing(15)

        # 启用功能复选框和红色提示
        blur_enable_layout = QVBoxLayout()
        self.blur_checkbox = QCheckBox("启用模糊去重")
        self.blur_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #cbd5e1;
            }
            QCheckBox::indicator:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border-color: #059669;
            }
        """)
        blur_enable_layout.addWidget(self.blur_checkbox)

        blur_warning_label = QLabel("⚠️ 注意：如果出现黑屏情况，请打开对应媒体源滤镜即可正常")
        blur_warning_label.setStyleSheet("""
            QLabel {
                color: #dc2626;
                font-size: 11pt;
                background: #fef2f2;
                padding: 8px;
                border-radius: 6px;
                border-left: 4px solid #dc2626;
            }
        """)
        blur_enable_layout.addWidget(blur_warning_label)
        blur_layout.addRow(blur_enable_layout)

        self.blur_filter_name_edit = QLineEdit(self.blur_control["filter_name"])
        self.blur_filter_name_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background: white;
                font-size: 11pt;
            }
            QLineEdit:focus {
                border-color: #667eea;
            }
        """)
        blur_layout.addRow("模糊滤镜名称:", self.blur_filter_name_edit)
        self.blur_min_radius_spin = QDoubleSpinBox()
        self.blur_min_radius_spin.setRange(0.0, 50.0)
        self.blur_min_radius_spin.setSingleStep(0.1)
        self.blur_min_radius_spin.setValue(self.blur_control["min_radius"])
        self.blur_min_radius_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QDoubleSpinBox:focus {
                border-color: #667eea;
            }
        """)
        blur_layout.addRow("最小半径:", self.blur_min_radius_spin)

        self.blur_max_radius_spin = QDoubleSpinBox()
        self.blur_max_radius_spin.setRange(0.0, 50.0)
        self.blur_max_radius_spin.setSingleStep(0.1)
        self.blur_max_radius_spin.setValue(self.blur_control["max_radius"])
        self.blur_max_radius_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QDoubleSpinBox:focus {
                border-color: #667eea;
            }
        """)
        blur_layout.addRow("最大半径:", self.blur_max_radius_spin)

        self.blur_interval_spin = QSpinBox()
        self.blur_interval_spin.setRange(100, 1000000)
        self.blur_interval_spin.setValue(self.blur_control["interval_ms"])
        self.blur_interval_spin.setSuffix(" ms")
        self.blur_interval_spin.setStyleSheet("""
            QSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QSpinBox:focus {
                border-color: #667eea;
            }
        """)
        blur_layout.addRow("变化间隔:", self.blur_interval_spin)

        # 添加下载链接
        import webbrowser
        download_blur_label = QLabel('<a href="https://lz.qaiu.top/parser?url=https://wwum.lanzoub.com/iltsO2ze4wxg&pwd=eotr">📥 下载模糊插件</a>')
        download_blur_label.setOpenExternalLinks(False)
        download_blur_label.setTextInteractionFlags(Qt.TextBrowserInteraction)
        download_blur_label.setStyleSheet("""
            QLabel {
                color: #667eea;
                font-weight: bold;
                text-decoration: underline;
                background: #f0f9ff;
                padding: 8px;
                border-radius: 6px;
                border: 1px solid #0ea5e9;
            }
        """)
        download_blur_label.setAlignment(Qt.AlignCenter)
        def open_blur_plugin_link():
            webbrowser.open("https://lz.qaiu.top/parser?url=https://wwum.lanzoub.com/iltsO2ze4wxg&pwd=eotr")
        download_blur_label.linkActivated.connect(lambda _: open_blur_plugin_link())
        blur_layout.addRow("", download_blur_label)

        # 连接信号
        self.blur_checkbox.stateChanged.connect(self.toggle_blur_control)
        self.blur_filter_name_edit.textChanged.connect(lambda text: self.update_blur_setting('filter_name', text))
        self.blur_min_radius_spin.valueChanged.connect(lambda value: self.update_blur_setting('min_radius', value))
        self.blur_max_radius_spin.valueChanged.connect(lambda value: self.update_blur_setting('max_radius', value))
        self.blur_interval_spin.valueChanged.connect(lambda value: self.update_blur_setting('interval_ms', value))

        video_sub_tabs.addTab(blur_tab, "🌫️ 模糊去重")

        # 子Tab 3: 视频移动去重
        transform_tab = QWidget()
        transform_layout = QFormLayout(transform_tab)
        transform_layout.setSpacing(15)

        # 启用开关
        self.transform_checkbox = QCheckBox("启用视频移动去重")
        self.transform_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #cbd5e1;
            }
            QCheckBox::indicator:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border-color: #059669;
            }
        """)
        transform_layout.addRow(self.transform_checkbox)

        # 缩放设置
        self.transform_scale_spin = QSpinBox()
        self.transform_scale_spin.setRange(100, 200)
        self.transform_scale_spin.setValue(self.transform_control["fixed_scale"])
        self.transform_scale_spin.setSingleStep(5)
        self.transform_scale_spin.setSuffix(" %")
        self.transform_scale_spin.setStyleSheet("""
            QSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QSpinBox:focus {
                border-color: #667eea;
            }
        """)
        self.transform_scale_spin.valueChanged.connect(lambda value: self.update_transform_setting('fixed_scale', value))
        transform_layout.addRow("缩放比例:", self.transform_scale_spin)

        # 移动间隔
        self.transform_interval_spin = QDoubleSpinBox()
        self.transform_interval_spin.setRange(0.5, 120.0)
        self.transform_interval_spin.setValue(self.transform_control["interval_secs"])
        self.transform_interval_spin.setSuffix(" 秒")
        self.transform_interval_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QDoubleSpinBox:focus {
                border-color: #667eea;
            }
        """)
        self.transform_interval_spin.valueChanged.connect(lambda value: self.update_transform_setting('interval_secs', value))
        transform_layout.addRow("移动间隔:", self.transform_interval_spin)

        # 过渡时间
        self.transform_transition_spin = QDoubleSpinBox()
        self.transform_transition_spin.setRange(0.5, 120)
        self.transform_transition_spin.setValue(self.transform_control["transition_secs"])
        self.transform_transition_spin.setSuffix(" 秒")
        self.transform_transition_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QDoubleSpinBox:focus {
                border-color: #667eea;
            }
        """)
        self.transform_transition_spin.valueChanged.connect(lambda value: self.update_transform_setting('transition_secs', value))
        transform_layout.addRow("过渡时间:", self.transform_transition_spin)

        # 连接信号
        self.transform_checkbox.stateChanged.connect(self.toggle_transform_control)

        video_sub_tabs.addTab(transform_tab, "↔️ 移动去重")

        # 子Tab 4: 颜色去重
        color_tab = QWidget()
        color_layout = QFormLayout(color_tab)
        color_layout.setSpacing(15)

        self.color_checkbox = QCheckBox("启用颜色去重")
        self.color_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #cbd5e1;
            }
            QCheckBox::indicator:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border-color: #059669;
            }
        """)
        color_layout.addRow(self.color_checkbox)

        self.color_filter_name_edit = QLineEdit(self.color_control["filter_name"])
        self.color_filter_name_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background: white;
                font-size: 11pt;
            }
            QLineEdit:focus {
                border-color: #667eea;
            }
        """)
        color_layout.addRow("颜色滤镜名称:", self.color_filter_name_edit)
        # 颜色范围设置函数
        def add_color_range_widgets(layout, ctrl, key, name, min_range, max_range, step):
            min_spin = QDoubleSpinBox()
            max_spin = QDoubleSpinBox()
            min_spin.setRange(min_range, max_range)
            min_spin.setSingleStep(step)
            max_spin.setRange(min_range, max_range)
            max_spin.setSingleStep(step)
            min_spin.setValue(ctrl[f'min_{key}'])
            max_spin.setValue(ctrl[f'max_{key}'])

            # 设置样式
            spinbox_style = """
                QDoubleSpinBox {
                    padding: 6px;
                    border: 2px solid #e2e8f0;
                    border-radius: 6px;
                    background: white;
                    min-width: 80px;
                }
                QDoubleSpinBox:focus {
                    border-color: #667eea;
                }
            """
            min_spin.setStyleSheet(spinbox_style)
            max_spin.setStyleSheet(spinbox_style)

            range_layout = QHBoxLayout()
            range_layout.setSpacing(8)
            min_label = QLabel("最小值:")
            min_label.setStyleSheet("font-weight: bold; color: #64748b;")
            max_label = QLabel("最大值:")
            max_label.setStyleSheet("font-weight: bold; color: #64748b;")

            range_layout.addWidget(min_label)
            range_layout.addWidget(min_spin)
            range_layout.addStretch(1)
            range_layout.addWidget(max_label)
            range_layout.addWidget(max_spin)

            range_widget = QWidget()
            range_widget.setLayout(range_layout)
            range_widget.setContentsMargins(0, 0, 0, 0)
            layout.addRow(f"{name} 范围:", range_widget)
            setattr(self, f"color_{key}_min_spin", min_spin)
            setattr(self, f"color_{key}_max_spin", max_spin)

            # 连接信号
            min_spin.valueChanged.connect(lambda value, k=key: self.update_color_setting(f'min_{k}', value))
            max_spin.valueChanged.connect(lambda value, k=key: self.update_color_setting(f'max_{k}', value))

        # 添加颜色范围控件
        add_color_range_widgets(color_layout, self.color_control, "hue", "色调", -180, 180, 1)
        add_color_range_widgets(color_layout, self.color_control, "brightness", "亮度", -1.0, 1.0, 0.01)
        add_color_range_widgets(color_layout, self.color_control, "contrast", "对比度", -4, 4, 0.01)
        add_color_range_widgets(color_layout, self.color_control, "saturation", "饱和度", -1, 5, 0.01)
        add_color_range_widgets(color_layout, self.color_control, "gamma", "伽马", -1, 1, 0.01)

        self.color_interval_spin = QSpinBox()
        self.color_interval_spin.setRange(100, 100000000)
        self.color_interval_spin.setValue(self.color_control["interval_ms"])
        self.color_interval_spin.setSuffix(" ms")
        self.color_interval_spin.setStyleSheet("""
            QSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QSpinBox:focus {
                border-color: #667eea;
            }
        """)
        color_layout.addRow("变化间隔:", self.color_interval_spin)

        # 连接信号
        self.color_checkbox.stateChanged.connect(self.toggle_color_control)
        self.color_filter_name_edit.textChanged.connect(lambda text: self.update_color_setting('filter_name', text))
        self.color_interval_spin.valueChanged.connect(lambda value: self.update_color_setting('interval_ms', value))

        video_sub_tabs.addTab(color_tab, "🎨 颜色去重")

        # 将视频子tabs添加到主视频tab
        video_main_layout.addWidget(video_sub_tabs)
        self.tab_widget.addTab(video_tab, "🎬 视频去重")

        # --- Tab 2: 音频去重 --- #
        audio_tab = QWidget()
        audio_main_layout = QVBoxLayout(audio_tab)

        # 音频去重标题和媒体源选择
        audio_header_layout = QVBoxLayout()
        audio_title = QLabel("🎵 音频去重功能")
        audio_title.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #667eea;
                padding: 10px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        audio_title.setAlignment(Qt.AlignCenter)
        audio_header_layout.addWidget(audio_title)

        # 统一的音频媒体源选择
        audio_source_layout = QHBoxLayout()
        audio_source_label = QLabel("音频媒体源:")
        audio_source_label.setStyleSheet("font-weight: bold; color: #1e293b;")
        self.audio_source_combo = QComboBox()
        self.audio_source_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background: white;
                min-height: 20px;
            }
            QComboBox:focus {
                border-color: #667eea;
            }
        """)
        refresh_button_audio = QPushButton("🔄 刷新媒体源")
        refresh_button_audio.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: #808080;
                border: none;
                padding: 8px 16px;
                border-radius: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
                transform: translateY(-1px);
            }
        """)
        refresh_button_audio.clicked.connect(self.refresh_media_sources)
        audio_source_layout.addWidget(audio_source_label)
        audio_source_layout.addWidget(self.audio_source_combo, 1)
        audio_source_layout.addWidget(refresh_button_audio)
        audio_header_layout.addLayout(audio_source_layout)
        audio_main_layout.addLayout(audio_header_layout)

        # 连接音频媒体源选择信号
        self.audio_source_combo.currentTextChanged.connect(self.update_audio_source_for_all_functions)

        # 创建音频功能的Tab控件
        audio_sub_tabs = QTabWidget()
        audio_sub_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #e2e8f0;
                border-radius: 12px;
                background: white;
            }
            QTabBar::tab {
                background: #f1f5f9;
                color: #64748b;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }
            QTabBar::tab:selected {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: black;
            }
            QTabBar::tab:hover {
                background: #e2e8f0;
            }
        """)

        # 子Tab 1: 音频EQ去重
        audio_eq_tab = QWidget()
        audio_eq_layout = QFormLayout(audio_eq_tab)
        audio_eq_layout.setSpacing(15)

        self.audio_checkbox = QCheckBox("启用音频EQ去重")
        self.audio_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #cbd5e1;
            }
            QCheckBox::indicator:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border-color: #059669;
            }
        """)
        audio_eq_layout.addRow(self.audio_checkbox)

        self.audio_filter_name_edit = QLineEdit(self.audio_eq_control["filter_name"])
        self.audio_filter_name_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background: white;
                font-size: 11pt;
            }
            QLineEdit:focus {
                border-color: #667eea;
            }
        """)
        audio_eq_layout.addRow("EQ滤镜名称:", self.audio_filter_name_edit)

        # 音频范围设置函数
        def add_audio_range_widgets(layout, ctrl, key, name):
            min_spin = QDoubleSpinBox()
            min_spin.setRange(-20, 20)
            min_spin.setSingleStep(0.1)
            min_spin.setValue(ctrl[f'min_{key}_gain'])
            min_spin.setSuffix(" dB")

            max_spin = QDoubleSpinBox()
            max_spin.setRange(-20, 20)
            max_spin.setSingleStep(0.1)
            max_spin.setValue(ctrl[f'max_{key}_gain'])
            max_spin.setSuffix(" dB")

            # 设置样式
            spinbox_style = """
                QDoubleSpinBox {
                    padding: 6px;
                    border: 2px solid #e2e8f0;
                    border-radius: 6px;
                    background: white;
                    min-width: 80px;
                }
                QDoubleSpinBox:focus {
                    border-color: #667eea;
                }
            """
            min_spin.setStyleSheet(spinbox_style)
            max_spin.setStyleSheet(spinbox_style)

            range_layout = QHBoxLayout()
            range_layout.setSpacing(8)
            min_label = QLabel("最小值:")
            min_label.setStyleSheet("font-weight: bold; color: #64748b;")
            max_label = QLabel("最大值:")
            max_label.setStyleSheet("font-weight: bold; color: #64748b;")

            range_layout.addWidget(min_label)
            range_layout.addWidget(min_spin)
            range_layout.addStretch(1)
            range_layout.addWidget(max_label)
            range_layout.addWidget(max_spin)

            range_widget = QWidget()
            range_widget.setLayout(range_layout)
            range_widget.setContentsMargins(0, 0, 0, 0)
            layout.addRow(f"{name} 增益范围:", range_widget)
            setattr(self, f"audio_{key}_min_spin", min_spin)
            setattr(self, f"audio_{key}_max_spin", max_spin)

            # 连接信号
            min_spin.valueChanged.connect(lambda value, k=key: self.update_audio_eq_setting(f'min_{k}_gain', value))
            max_spin.valueChanged.connect(lambda value, k=key: self.update_audio_eq_setting(f'max_{k}_gain', value))

        add_audio_range_widgets(audio_eq_layout, self.audio_eq_control, "low", "低频")
        add_audio_range_widgets(audio_eq_layout, self.audio_eq_control, "mid", "中频")
        add_audio_range_widgets(audio_eq_layout, self.audio_eq_control, "high", "高频")

        self.audio_interval_spin = QDoubleSpinBox()
        self.audio_interval_spin.setRange(0.1, 120.0)
        self.audio_interval_spin.setValue(self.audio_eq_control["interval_secs"])
        self.audio_interval_spin.setSuffix(" s")
        self.audio_interval_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QDoubleSpinBox:focus {
                border-color: #667eea;
            }
        """)
        audio_eq_layout.addRow("变化间隔:", self.audio_interval_spin)

        # 连接信号
        self.audio_checkbox.stateChanged.connect(self.toggle_audio_eq_control)
        self.audio_filter_name_edit.textChanged.connect(lambda text: self.update_audio_eq_setting('filter_name', text))
        self.audio_interval_spin.valueChanged.connect(lambda value: self.update_audio_eq_setting('interval_secs', value))

        audio_sub_tabs.addTab(audio_eq_tab, "🔊 音频EQ去重")

        # 子Tab 2: 断音控制
        audio_mute_tab = QWidget()
        audio_mute_layout = QFormLayout(audio_mute_tab)
        audio_mute_layout.setSpacing(15)

        self.audio_mute_checkbox = QCheckBox("启用断音控制")
        self.audio_mute_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #cbd5e1;
            }
            QCheckBox::indicator:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border-color: #059669;
            }
        """)
        self.audio_mute_checkbox.stateChanged.connect(self.toggle_audio_mute_control)
        audio_mute_layout.addRow(self.audio_mute_checkbox)

        # 断音间隔范围设置
        interval_range_widget = QWidget()
        interval_range_layout = QVBoxLayout(interval_range_widget)
        interval_range_layout.setSpacing(8)

        # 最小间隔
        interval_min_layout = QHBoxLayout()
        interval_min_label = QLabel("最小间隔:")
        interval_min_label.setStyleSheet("font-weight: bold; color: #64748b;")
        self.audio_mute_interval_min_spin = QDoubleSpinBox()
        self.audio_mute_interval_min_spin.setRange(10.0, 120.0)  # 扩大范围以支持30-40秒
        self.audio_mute_interval_min_spin.setValue(self.audio_mute_control["min_interval_secs"])
        self.audio_mute_interval_min_spin.setSuffix(" s")
        self.audio_mute_interval_min_spin.setSingleStep(1.0)  # 调整步长为1秒
        self.audio_mute_interval_min_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QDoubleSpinBox:focus {
                border-color: #667eea;
            }
        """)
        interval_min_layout.addWidget(interval_min_label)
        interval_min_layout.addWidget(self.audio_mute_interval_min_spin)
        interval_min_layout.addStretch(1)

        # 最大间隔
        interval_max_layout = QHBoxLayout()
        interval_max_label = QLabel("最大间隔:")
        interval_max_label.setStyleSheet("font-weight: bold; color: #64748b;")
        self.audio_mute_interval_max_spin = QDoubleSpinBox()
        self.audio_mute_interval_max_spin.setRange(10.0, 120.0)  # 扩大范围以支持30-40秒
        self.audio_mute_interval_max_spin.setValue(self.audio_mute_control["max_interval_secs"])
        self.audio_mute_interval_max_spin.setSuffix(" s")
        self.audio_mute_interval_max_spin.setSingleStep(1.0)  # 调整步长为1秒
        self.audio_mute_interval_max_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QDoubleSpinBox:focus {
                border-color: #667eea;
            }
        """)
        interval_max_layout.addWidget(interval_max_label)
        interval_max_layout.addWidget(self.audio_mute_interval_max_spin)
        interval_max_layout.addStretch(1)

        interval_range_layout.addLayout(interval_min_layout)
        interval_range_layout.addLayout(interval_max_layout)
        audio_mute_layout.addRow("断音间隔范围:", interval_range_widget)

        # 断音持续时间范围设置
        duration_range_widget = QWidget()
        duration_range_layout = QVBoxLayout(duration_range_widget)
        duration_range_layout.setSpacing(8)

        # 最短时间
        duration_min_layout = QHBoxLayout()
        duration_min_label = QLabel("最短时间:")
        duration_min_label.setStyleSheet("font-weight: bold; color: #64748b;")
        self.audio_mute_duration_min_spin = QDoubleSpinBox()
        self.audio_mute_duration_min_spin.setRange(0.1, 2.0)  # 调整范围以适应0.1-0.5秒
        self.audio_mute_duration_min_spin.setValue(self.audio_mute_control["min_mute_duration_secs"])
        self.audio_mute_duration_min_spin.setSuffix(" s")
        self.audio_mute_duration_min_spin.setSingleStep(0.1)
        self.audio_mute_duration_min_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QDoubleSpinBox:focus {
                border-color: #667eea;
            }
        """)
        duration_min_layout.addWidget(duration_min_label)
        duration_min_layout.addWidget(self.audio_mute_duration_min_spin)
        duration_min_layout.addStretch(1)

        # 最长时间
        duration_max_layout = QHBoxLayout()
        duration_max_label = QLabel("最长时间:")
        duration_max_label.setStyleSheet("font-weight: bold; color: #64748b;")
        self.audio_mute_duration_max_spin = QDoubleSpinBox()
        self.audio_mute_duration_max_spin.setRange(0.1, 2.0)  # 调整范围以适应0.1-0.5秒
        self.audio_mute_duration_max_spin.setValue(self.audio_mute_control["max_mute_duration_secs"])
        self.audio_mute_duration_max_spin.setSuffix(" s")
        self.audio_mute_duration_max_spin.setSingleStep(0.1)
        self.audio_mute_duration_max_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QDoubleSpinBox:focus {
                border-color: #667eea;
            }
        """)
        duration_max_layout.addWidget(duration_max_label)
        duration_max_layout.addWidget(self.audio_mute_duration_max_spin)
        duration_max_layout.addStretch(1)

        duration_range_layout.addLayout(duration_min_layout)
        duration_range_layout.addLayout(duration_max_layout)
        audio_mute_layout.addRow("断音持续时间范围:", duration_range_widget)

        # 连接信号
        self.audio_mute_interval_min_spin.valueChanged.connect(lambda value: self.update_audio_mute_setting('min_interval_secs', value))
        self.audio_mute_interval_max_spin.valueChanged.connect(lambda value: self.update_audio_mute_setting('max_interval_secs', value))
        self.audio_mute_duration_min_spin.valueChanged.connect(lambda value: self.update_audio_mute_setting('min_mute_duration_secs', value))
        self.audio_mute_duration_max_spin.valueChanged.connect(lambda value: self.update_audio_mute_setting('max_mute_duration_secs', value))

        audio_sub_tabs.addTab(audio_mute_tab, "🔇 断音控制")

        # 子Tab 3: 随机音频播放大小控制
        audio_volume_tab = QWidget()
        audio_volume_layout = QFormLayout(audio_volume_tab)
        audio_volume_layout.setSpacing(15)

        self.audio_volume_checkbox = QCheckBox("启用随机音频播放大小控制")
        self.audio_volume_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #cbd5e1;
            }
            QCheckBox::indicator:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border-color: #059669;
            }
        """)
        self.audio_volume_checkbox.stateChanged.connect(self.toggle_audio_volume_control)
        audio_volume_layout.addRow(self.audio_volume_checkbox)

        # 音量百分比范围设置
        volume_range_widget = QWidget()
        volume_range_layout = QVBoxLayout(volume_range_widget)
        volume_range_layout.setSpacing(8)

        # 最小音量
        volume_min_layout = QHBoxLayout()
        volume_min_label = QLabel("最小音量:")
        volume_min_label.setStyleSheet("font-weight: bold; color: #64748b;")
        self.audio_volume_min_spin = QSpinBox()
        self.audio_volume_min_spin.setRange(0, 100)
        self.audio_volume_min_spin.setValue(self.audio_volume_control["volume_min_percent"])
        self.audio_volume_min_spin.setSuffix(" %")
        self.audio_volume_min_spin.setSingleStep(1)
        self.audio_volume_min_spin.setStyleSheet("""
            QSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QSpinBox:focus {
                border-color: #667eea;
            }
        """)
        volume_min_layout.addWidget(volume_min_label)
        volume_min_layout.addWidget(self.audio_volume_min_spin)
        volume_min_layout.addStretch(1)

        # 最大音量
        volume_max_layout = QHBoxLayout()
        volume_max_label = QLabel("最大音量:")
        volume_max_label.setStyleSheet("font-weight: bold; color: #64748b;")
        self.audio_volume_max_spin = QSpinBox()
        self.audio_volume_max_spin.setRange(0, 100)
        self.audio_volume_max_spin.setValue(self.audio_volume_control["volume_max_percent"])
        self.audio_volume_max_spin.setSuffix(" %")
        self.audio_volume_max_spin.setSingleStep(1)
        self.audio_volume_max_spin.setStyleSheet("""
            QSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QSpinBox:focus {
                border-color: #667eea;
            }
        """)
        volume_max_layout.addWidget(volume_max_label)
        volume_max_layout.addWidget(self.audio_volume_max_spin)
        volume_max_layout.addStretch(1)

        volume_range_layout.addLayout(volume_min_layout)
        volume_range_layout.addLayout(volume_max_layout)
        audio_volume_layout.addRow("音量百分比范围:", volume_range_widget)

        # 间隔时间范围设置
        interval_range_widget = QWidget()
        interval_range_layout = QVBoxLayout(interval_range_widget)
        interval_range_layout.setSpacing(8)

        # 最小间隔
        interval_min_layout = QHBoxLayout()
        interval_min_label = QLabel("最小间隔:")
        interval_min_label.setStyleSheet("font-weight: bold; color: #64748b;")
        self.audio_volume_interval_min_spin = QDoubleSpinBox()
        self.audio_volume_interval_min_spin.setRange(0.1, 120.0)
        self.audio_volume_interval_min_spin.setValue(self.audio_volume_control["min_interval_secs"])
        self.audio_volume_interval_min_spin.setSuffix(" s")
        self.audio_volume_interval_min_spin.setSingleStep(0.1)
        self.audio_volume_interval_min_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QDoubleSpinBox:focus {
                border-color: #667eea;
            }
        """)
        interval_min_layout.addWidget(interval_min_label)
        interval_min_layout.addWidget(self.audio_volume_interval_min_spin)
        interval_min_layout.addStretch(1)

        # 最大间隔
        interval_max_layout = QHBoxLayout()
        interval_max_label = QLabel("最大间隔:")
        interval_max_label.setStyleSheet("font-weight: bold; color: #64748b;")
        self.audio_volume_interval_max_spin = QDoubleSpinBox()
        self.audio_volume_interval_max_spin.setRange(0.1, 120.0)
        self.audio_volume_interval_max_spin.setValue(self.audio_volume_control["max_interval_secs"])
        self.audio_volume_interval_max_spin.setSuffix(" s")
        self.audio_volume_interval_max_spin.setSingleStep(0.1)
        self.audio_volume_interval_max_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QDoubleSpinBox:focus {
                border-color: #667eea;
            }
        """)
        interval_max_layout.addWidget(interval_max_label)
        interval_max_layout.addWidget(self.audio_volume_interval_max_spin)
        interval_max_layout.addStretch(1)

        interval_range_layout.addLayout(interval_min_layout)
        interval_range_layout.addLayout(interval_max_layout)
        audio_volume_layout.addRow("调整间隔范围:", interval_range_widget)

        # 连接信号
        self.audio_volume_min_spin.valueChanged.connect(lambda value: self.update_audio_volume_setting('volume_min_percent', value))
        self.audio_volume_max_spin.valueChanged.connect(lambda value: self.update_audio_volume_setting('volume_max_percent', value))
        self.audio_volume_interval_min_spin.valueChanged.connect(lambda value: self.update_audio_volume_setting('min_interval_secs', value))
        self.audio_volume_interval_max_spin.valueChanged.connect(lambda value: self.update_audio_volume_setting('max_interval_secs', value))

        audio_sub_tabs.addTab(audio_volume_tab, "🎚️音频大小")

        # 将音频子tabs添加到主音频tab
        audio_main_layout.addWidget(audio_sub_tabs)
        self.tab_widget.addTab(audio_tab, "🎵 音频去重")

        # --- Tab 3: 说明书 ---
        instructions_tab = QWidget()
        instructions_layout = QHBoxLayout(instructions_tab)
        instructions_layout.setSpacing(30)
        instructions_layout.setContentsMargins(25, 25, 25, 25)

        # 左侧文字说明 - 使用滚动区域
        left_scroll = QScrollArea()
        left_scroll.setWidgetResizable(True)
        left_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        left_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        left_scroll.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollBar:vertical {
                background: #f1f5f9;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: #cbd5e1;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #94a3b8;
            }
        """)

        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setSpacing(15)
        instructions_label = QLabel("""
        <div style='background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); padding: 25px; border-radius: 15px; border: 2px solid #e2e8f0; margin-bottom: 25px;'>
            <h1 style='color: #667eea; text-align: center; margin: 0; font-size: 24pt; font-weight: bold;'>🎬 OBS 去重软件使用说明书 v1.3</h1>
            <p style='color: #64748b; text-align: center; margin: 10px 0 0 0; font-size: 12pt;'>  智能去重 · 一刀不剪</p>
        </div>

        <div style='margin-top: 20px;'>
            <h2 style='color: #059669; border-bottom: 3px solid #059669; padding-bottom: 8px; font-size: 18pt; margin-bottom: 15px; font-weight: bold;'>📋 软件简介</h2>
            <div style='background: #ffffff; padding: 20px; border-radius: 12px; border: 2px solid #e2e8f0; border-left: 5px solid #667eea; margin-bottom: 25px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);'>
                <p style='color: #0f172a; line-height: 1.8; margin: 0; font-size: 13pt; font-weight: 500;'>
                    欢迎使用全新升级的OBS去重软件！本软件采用现代化UI设计，专为OBS设计的智能辅助工具，
                    用于日常直播的视频和音频去重，让您可以做到一刀不剪稳定直播。现在功能更加集中，
                    操作更加便捷，界面更加美观！
                </p>
            </div>

            <h2 style='color: #059669; border-bottom: 3px solid #059669; padding-bottom: 8px; font-size: 18pt; margin-bottom: 15px; font-weight: bold;'>🎯 主要功能</h2>

            <div style='background: #ffffff; padding: 20px; border-radius: 12px; border: 2px solid #0ea5e9; margin-bottom: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);'>
                <h3 style='color: #0c4a6e; margin: 0 0 15px 0; font-size: 16pt; text-align: center; font-weight: bold;'>🎬 视频去重板块</h3>
                <div style='display: grid; grid-template-columns: 1fr 1fr; gap: 12px;'>
                    <div style='background: #f0f9ff; padding: 12px; border-radius: 8px; border: 1px solid #bae6fd;'>
                        <strong style='color: #0c4a6e; font-size: 12pt;'>🚀 智能加减速</strong><br/>
                        <span style='color: #0f172a; font-size: 11pt; font-weight: 500;'>自动调节视频播放速度</span>
                    </div>
                    <div style='background: #f0f9ff; padding: 12px; border-radius: 8px; border: 1px solid #bae6fd;'>
                        <strong style='color: #0c4a6e; font-size: 12pt;'>🌫️ 模糊效果</strong><br/>
                        <span style='color: #0f172a; font-size: 11pt; font-weight: 500;'>智能调节画面模糊度</span>
                    </div>
                    <div style='background: #f0f9ff; padding: 12px; border-radius: 8px; border: 1px solid #bae6fd;'>
                        <strong style='color: #0c4a6e; font-size: 12pt;'>↔️ 视频移动</strong><br/>
                        <span style='color: #0f172a; font-size: 11pt; font-weight: 500;'>自动调整画面位置和大小</span>
                    </div>
                    <div style='background: #f0f9ff; padding: 12px; border-radius: 8px; border: 1px solid #bae6fd;'>
                        <strong style='color: #0c4a6e; font-size: 12pt;'>🎨 颜色调节</strong><br/>
                        <span style='color: #0f172a; font-size: 11pt; font-weight: 500;'>动态调整视频色彩参数</span>
                    </div>
                </div>
            </div>

            <div style='background: #ffffff; padding: 20px; border-radius: 12px; border: 2px solid #10b981; margin-bottom: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);'>
                <h3 style='color: #064e3b; margin: 0 0 15px 0; font-size: 16pt; text-align: center; font-weight: bold;'>🎵 音频去重板块</h3>
                <div style='display: grid; grid-template-columns: 1fr 1fr; gap: 12px;'>
                    <div style='background: #f0fdf4; padding: 12px; border-radius: 8px; border: 1px solid #bbf7d0;'>
                        <strong style='color: #064e3b; font-size: 12pt;'>🔊 音频EQ</strong><br/>
                        <span style='color: #0f172a; font-size: 11pt; font-weight: 500;'>实时调节音频均衡器参数</span>
                    </div>
                    <div style='background: #f0fdf4; padding: 12px; border-radius: 8px; border: 1px solid #bbf7d0;'>
                        <strong style='color: #064e3b; font-size: 12pt;'>🔇 断音控制</strong><br/>
                        <span style='color: #0f172a; font-size: 11pt; font-weight: 500;'>随机间隔自动断音</span>
                    </div>
                    <div style='background: #f0fdf4; padding: 12px; border-radius: 8px; border: 1px solid #bbf7d0;'>
                        <strong style='color: #064e3b; font-size: 12pt;'>🎚️ 音量调节</strong><br/>
                        <span style='color: #0f172a; font-size: 11pt; font-weight: 500;'>随机调整音频音量百分比</span>
                    </div>
                    <div style='background: #f0fdf4; padding: 12px; border-radius: 8px; border: 1px solid #bbf7d0;'>
                        <strong style='color: #064e3b; font-size: 12pt;'>🗜️ 自动压缩</strong><br/>
                        <span style='color: #0f172a; font-size: 11pt; font-weight: 500;'>智能音频压缩处理</span>
                    </div>
                    <div style='background: #f0fdf4; padding: 12px; border-radius: 8px; border: 1px solid #bbf7d0; '>
                        <strong style='color: #064e3b; font-size: 12pt;'>🎚️ 自动增益</strong><br/>
                        <span style='color: #0f172a; font-size: 11pt; font-weight: 500;'>自动调节音频增益大小</span>
                    </div>
                </div>
            </div>

            <h2 style='color: #1d4ed8; border-bottom: 3px solid #1d4ed8; padding-bottom: 8px; font-size: 18pt; margin-bottom: 15px; font-weight: bold;'>📝 使用步骤</h2>
            <div style='background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); padding: 25px; border-radius: 15px; border: 2px solid #3b82f6; margin-bottom: 20px; box-shadow: 0 6px 12px rgba(59, 130, 246, 0.15);'>
                <div style='counter-reset: step-counter;'>
                    <div style='background: #ffffff; padding: 18px; border-radius: 10px; margin-bottom: 15px; border: 2px solid #dbeafe; position: relative; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);'>
                        <div style='position: absolute; left: -12px; top: -12px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 15pt; box-shadow: 0 3px 6px rgba(59, 130, 246, 0.3);'>1</div>
                        <strong style='color: #1e40af; margin-left: 30px; font-size: 13pt;'>连接OBS：</strong>
                        <span style='color: #1e293b; font-weight: 500; font-size: 12pt;'>确保OBS已启动，在【设置→WebSocket】中开启远程控制（默认端口4455）</span>
                    </div>
                    <div style='background: #ffffff; padding: 18px; border-radius: 10px; margin-bottom: 15px; border: 2px solid #dbeafe; position: relative; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);'>
                        <div style='position: absolute; left: -12px; top: -12px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 15pt; box-shadow: 0 3px 6px rgba(59, 130, 246, 0.3);'>2</div>
                        <strong style='color: #1e40af; margin-left: 30px; font-size: 13pt;'>建立连接：</strong>
                        <span style='color: #1e293b; font-weight: 500; font-size: 12pt;'>点击软件右上角的【连接OBS】按钮</span>
                    </div>
                    <div style='background: #ffffff; padding: 18px; border-radius: 10px; margin-bottom: 15px; border: 2px solid #dbeafe; position: relative; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);'>
                        <div style='position: absolute; left: -12px; top: -12px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 15pt; box-shadow: 0 3px 6px rgba(59, 130, 246, 0.3);'>3</div>
                        <strong style='color: #1e40af; margin-left: 30px; font-size: 13pt;'>选择媒体源：</strong>
                        <span style='color: #1e293b; font-weight: 500; font-size: 12pt;'>在视频/音频去重板块中分别选择对应的媒体源</span>
                    </div>
                    <div style='background: #ffffff; padding: 18px; border-radius: 10px; margin-bottom: 15px; border: 2px solid #dbeafe; position: relative; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);'>
                        <div style='position: absolute; left: -12px; top: -12px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 15pt; box-shadow: 0 3px 6px rgba(59, 130, 246, 0.3);'>4</div>
                        <strong style='color: #1e40af; margin-left: 30px; font-size: 13pt;'>配置参数：</strong>
                        <span style='color: #1e293b; font-weight: 500; font-size: 12pt;'>根据需要在各子功能中调整参数</span>
                    </div>
                    <div style='background: #ffffff; padding: 18px; border-radius: 10px; border: 2px solid #dbeafe; position: relative; box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);'>
                        <div style='position: absolute; left: -12px; top: -12px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 15pt; box-shadow: 0 3px 6px rgba(59, 130, 246, 0.3);'>5</div>
                        <strong style='color: #1e40af; margin-left: 30px; font-size: 13pt;'>启用功能：</strong>
                        <span style='color: #1e293b; font-weight: 500; font-size: 12pt;'>勾选【启用】开始运行相应的去重功能</span>
                    </div>
                </div>
            </div>

            <h2 style='color: #b91c1c; border-bottom: 3px solid #b91c1c; padding-bottom: 8px; font-size: 18pt; margin-bottom: 15px; font-weight: bold;'>⚠️ 注意事项</h2>
            <div style='background: #ffffff; padding: 20px; border-radius: 12px; border: 2px solid #dc2626; margin-bottom: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);'>
                <div style='display: grid; gap: 12px;'>
                    <div style='background: #fef2f2; padding: 15px; border-radius: 8px; border: 1px solid #fecaca; border-left: 4px solid #dc2626;'>
                        <strong style='color: #b91c1c; font-size: 12pt;'>🔌 插件依赖：</strong>
                        <span style='color: #0f172a; font-weight: 500;'>使用模糊功能前，请先安装Composite Blur滤镜插件</span>
                    </div>
                    <div style='background: #fef2f2; padding: 15px; border-radius: 8px; border: 1px solid #fecaca; border-left: 4px solid #dc2626;'>
                        <strong style='color: #b91c1c; font-size: 12pt;'>📺 媒体源：</strong>
                        <span style='color: #0f172a; font-weight: 500;'>视频功能共享同一个视频媒体源，音频功能共享同一个音频媒体源</span>
                    </div>
                    <div style='background: #fef2f2; padding: 15px; border-radius: 8px; border: 1px solid #fecaca; border-left: 4px solid #dc2626;'>
                        <strong style='color: #b91c1c; font-size: 12pt;'>💾 设置保存：</strong>
                        <span style='color: #0f172a; font-weight: 500;'>所有设置会自动保存，重启软件后仍然生效</span>
                    </div>
                    <div style='background: #fef2f2; padding: 15px; border-radius: 8px; border: 1px solid #fecaca; border-left: 4px solid #dc2626;'>
                        <strong style='color: #b91c1c; font-size: 12pt;'>🔄 重置设置：</strong>
                        <span style='color: #0f172a; font-weight: 500;'>如需恢复默认设置，请点击左下角的【恢复默认设置】按钮</span>
                    </div>
                </div>
            </div>
        </div>
        """)
        instructions_label.setTextFormat(Qt.RichText)
        instructions_label.setAlignment(Qt.AlignTop)
        instructions_label.setWordWrap(True)
        instructions_label.setStyleSheet("""
            QLabel {
                background: transparent;
                font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            }
        """)
        left_layout.addWidget(instructions_label)
        left_layout.addStretch()  # 添加弹性空间

        # 将内容widget设置到滚动区域
        left_scroll.setWidget(left_widget)

        # 右侧联系信息
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setAlignment(Qt.AlignCenter)

        # 联系信息卡片
        contact_card = QLabel()
        contact_card.setText("""
        <div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 25px; border-radius: 15px; text-align: center; color: black;'>
            <h3 style='margin: 0 0 15px 0; color: black;'>💬 技术支持</h3>
            <p style='margin: 8px 0; font-size: 14pt; color: black;'><strong>作者：阿昊</strong></p>
            <p style='margin: 8px 0; font-size: 14pt; color: black;'><strong>微信：LT865432</strong></p>
            <div style='margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.2);
                        border-radius: 10px; border: 1px solid rgba(255,255,255,0.3);'>
                <p style='margin: 0; font-size: 12pt; color: black;'>🛠️ 遇到问题随时联系</p>
                <p style='margin: 5px 0 0 0; font-size: 12pt; color: black;'>💡 建议反馈欢迎提出</p>
            </div>
        </div>
        """)
        contact_card.setTextFormat(Qt.RichText)
        contact_card.setAlignment(Qt.AlignCenter)
        right_layout.addWidget(contact_card)

        # 添加二维码图片
        qr_label = QLabel()
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            qr_path = os.path.join(current_dir, "zuozhe.png")
            # print(f"尝试加载二维码图片: {qr_path}")

            qr_pixmap = QPixmap(qr_path)
            if qr_pixmap.isNull():
                # print("警告：无法加载二维码图片")
                qr_label.setText("📱 扫码加微信")
                qr_label.setStyleSheet("""
                    QLabel {
                        background: #f1f5f9;
                        border: 2px dashed #cbd5e1;
                        border-radius: 10px;
                        padding: 20px;
                        color: #64748b;
                        font-size: 14pt;
                    }
                """)
            else:
                # print("成功加载二维码图片")
                scaled_pixmap = qr_pixmap.scaled(180, 180, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                qr_label.setPixmap(scaled_pixmap)
                qr_label.setStyleSheet("""
                    QLabel {
                        background: white;
                        border: 3px solid #e2e8f0;
                        border-radius: 12px;
                        padding: 10px;
                    }
                """)
        except Exception as e:
            print(f"加载二维码图片时出错: {str(e)}")
            qr_label.setText("📱 扫码加微信")
            qr_label.setStyleSheet("""
                QLabel {
                    background: #f1f5f9;
                    border: 2px dashed #cbd5e1;
                    border-radius: 10px;
                    padding: 20px;
                    color: #64748b;
                    font-size: 14pt;
                }
            """)

        qr_label.setAlignment(Qt.AlignCenter)
        right_layout.addWidget(qr_label)

        # 添加提示文字
        qr_text = QLabel("📱 扫码联系作者")
        qr_text.setAlignment(Qt.AlignCenter)
        qr_text.setStyleSheet("""
            QLabel {
                font-size: 14pt;
                color: #667eea;
                font-weight: bold;
                margin-top: 10px;
                padding: 8px;
                background: #f0f9ff;
                border-radius: 8px;
                border: 1px solid #0ea5e9;
            }
        """)
        right_layout.addWidget(qr_text)

        # 将左右两部分添加到主布局
        instructions_layout.addWidget(left_scroll, stretch=7)  # 使用滚动区域
        instructions_layout.addWidget(right_widget, stretch=3)

        # --- Tab 3: 音频播放器 --- #
        audio_player_tab = QWidget()
        self.create_audio_player_tab(audio_player_tab)
        self.tab_widget.addTab(audio_player_tab, "🎵 音频播放器")

        # --- Tab 4: 爆闪播放器 --- #
        flash_player_tab = QWidget()
        self.create_flash_player_tab(flash_player_tab)
        self.tab_widget.addTab(flash_player_tab, "⚡ 爆闪播放器")

        self.tab_widget.addTab(instructions_tab, "📖 说明书")
        # ---------------------

        # --- 底部状态栏 ---

        # --- 将 Tab 控件添加到主布局 --- #
        main_layout.addWidget(self.tab_widget)

        # --- 新增：添加底部按钮区域 ---
        buttons_layout = QHBoxLayout()
        
        # 恢复默认设置按钮
        self.restore_defaults_btn = QPushButton("恢复默认设置")
        self.restore_defaults_btn.setStyleSheet("""
            QPushButton {
                background-color: #d35f5f;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #c74545;
            }
        """)
        self.restore_defaults_btn.clicked.connect(self.confirm_restore_defaults)
        buttons_layout.addWidget(self.restore_defaults_btn)
        
        # 右侧添加作者信息（添加弹性空间）
        buttons_layout.addStretch()
        
        # --- 添加作者信息标签 ---
        author_label = QLabel("作者：阿昊\n有问题随时联系\nWX：LT865432") # 使用换行符
        # 设置文本右对齐，垂直居中
        author_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)
        # 可以选择性地设置一点样式，例如稍微小一点的灰色字体
        author_label.setStyleSheet("color: grey; font-size: 20pt;") # 使用用户当前的字号
        buttons_layout.addWidget(author_label)
        # ---------------------------
        
        # 添加按钮区域到主布局，并增加一点间距
        main_layout.addSpacing(10)
        main_layout.addLayout(buttons_layout)
        # ---------------------------

        # 子Tab 4: 自动压缩
        compressor_tab = QWidget()
        compressor_layout = QFormLayout(compressor_tab)
        compressor_layout.setSpacing(15)

        # 启用开关
        self.compressor_enable_checkbox = QCheckBox("启用自动压缩")
        self.compressor_enable_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #cbd5e1;
            }
            QCheckBox::indicator:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border-color: #059669;
            }
        """)
        compressor_layout.addRow(self.compressor_enable_checkbox)

        # 滤镜名称
        self.compressor_filter_name_edit = QLineEdit(self.compressor_control["filter_name"])
        self.compressor_filter_name_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background: white;
                font-size: 11pt;
            }
            QLineEdit:focus {
                border-color: #667eea;
            }
        """)
        compressor_layout.addRow("压缩滤镜名称:", self.compressor_filter_name_edit)
        
        # 压缩器范围设置函数
        def add_compressor_range_widgets(layout, ctrl, key, name, min_range, max_range, step, suffix=""):
            min_spin = QDoubleSpinBox()
            max_spin = QDoubleSpinBox()
            min_spin.setRange(min_range, max_range)
            max_spin.setRange(min_range, max_range)
            min_spin.setSingleStep(step)
            max_spin.setSingleStep(step)
            min_spin.setValue(ctrl[f'min_{key}'])
            max_spin.setValue(ctrl[f'max_{key}'])
            if suffix:
                min_spin.setSuffix(suffix)
                max_spin.setSuffix(suffix)

            # 设置样式
            spinbox_style = """
                QDoubleSpinBox {
                    padding: 6px;
                    border: 2px solid #e2e8f0;
                    border-radius: 6px;
                    background: white;
                    min-width: 80px;
                }
                QDoubleSpinBox:focus {
                    border-color: #667eea;
                }
            """
            min_spin.setStyleSheet(spinbox_style)
            max_spin.setStyleSheet(spinbox_style)

            range_layout = QHBoxLayout()
            range_layout.setSpacing(8)
            min_label = QLabel("最小值:")
            min_label.setStyleSheet("font-weight: bold; color: #64748b;")
            max_label = QLabel("最大值:")
            max_label.setStyleSheet("font-weight: bold; color: #64748b;")

            range_layout.addWidget(min_label)
            range_layout.addWidget(min_spin)
            range_layout.addStretch(1)
            range_layout.addWidget(max_label)
            range_layout.addWidget(max_spin)

            range_widget = QWidget()
            range_widget.setLayout(range_layout)
            range_widget.setContentsMargins(0, 0, 0, 0)
            layout.addRow(f"{name} 范围:", range_widget)
            setattr(self, f"compressor_{key}_min_spin", min_spin)
            setattr(self, f"compressor_{key}_max_spin", max_spin)

            # 连接信号
            min_spin.valueChanged.connect(lambda value, k=key: self.update_compressor_setting(f'min_{k}', value))
            max_spin.valueChanged.connect(lambda value, k=key: self.update_compressor_setting(f'max_{k}', value))

        # 添加压缩器参数范围控件
        add_compressor_range_widgets(compressor_layout, self.compressor_control, "ratio", "比率", 1, 20, 0.1)
        add_compressor_range_widgets(compressor_layout, self.compressor_control, "threshold", "阈值", -60, 0, 0.1, " dB")
        add_compressor_range_widgets(compressor_layout, self.compressor_control, "output_gain", "输出增益", -10, 10, 0.1, " dB")
        add_compressor_range_widgets(compressor_layout, self.compressor_control, "release", "释放时间", 10, 1000, 1, " ms")

        # 变化间隔
        self.compressor_interval_spin = QDoubleSpinBox()
        self.compressor_interval_spin.setRange(0.1, 120.0)
        self.compressor_interval_spin.setValue(self.compressor_control["interval_secs"])
        self.compressor_interval_spin.setSuffix(" s")
        self.compressor_interval_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QDoubleSpinBox:focus {
                border-color: #667eea;
            }
        """)
        compressor_layout.addRow("变化间隔:", self.compressor_interval_spin)

        # 连接信号
        self.compressor_enable_checkbox.toggled.connect(self.toggle_compressor_control)
        self.compressor_filter_name_edit.textChanged.connect(lambda text: self.update_compressor_setting('filter_name', text))
        self.compressor_interval_spin.valueChanged.connect(lambda v: self.update_compressor_setting("interval_secs", v))

        audio_sub_tabs.addTab(compressor_tab, "🗜️ 自动压缩")
        # 子Tab 5: 自动增益
        gain_tab = QWidget()
        gain_layout = QFormLayout(gain_tab)
        gain_layout.setSpacing(15)

        # 启用开关
        self.gain_enable_checkbox = QCheckBox("启用自动增益")
        self.gain_enable_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #cbd5e1;
            }
            QCheckBox::indicator:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border-color: #059669;
            }
        """)
        gain_layout.addRow(self.gain_enable_checkbox)

        # 滤镜名称
        self.gain_filter_name_edit = QLineEdit(self.gain_control["filter_name"])
        self.gain_filter_name_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background: white;
                font-size: 11pt;
            }
            QLineEdit:focus {
                border-color: #667eea;
            }
        """)
        gain_layout.addRow("增益滤镜名称:", self.gain_filter_name_edit)
        
        # 增益范围设置函数
        def add_gain_range_widgets(layout, ctrl, key, name, min_range, max_range, step, suffix=""):
            min_spin = QDoubleSpinBox()
            max_spin = QDoubleSpinBox()
            min_spin.setRange(min_range, max_range)
            max_spin.setRange(min_range, max_range)
            min_spin.setSingleStep(step)
            max_spin.setSingleStep(step)
            min_spin.setValue(ctrl[f'min_{key}'])
            max_spin.setValue(ctrl[f'max_{key}'])
            if suffix:
                min_spin.setSuffix(suffix)
                max_spin.setSuffix(suffix)

            # 设置样式
            spinbox_style = """
                QDoubleSpinBox {
                    padding: 6px;
                    border: 2px solid #e2e8f0;
                    border-radius: 6px;
                    background: white;
                    min-width: 80px;
                }
                QDoubleSpinBox:focus {
                    border-color: #667eea;
                }
            """
            min_spin.setStyleSheet(spinbox_style)
            max_spin.setStyleSheet(spinbox_style)

            range_layout = QHBoxLayout()
            range_layout.setSpacing(8)
            min_label = QLabel("最小值:")
            min_label.setStyleSheet("font-weight: bold; color: #64748b;")
            max_label = QLabel("最大值:")
            max_label.setStyleSheet("font-weight: bold; color: #64748b;")

            range_layout.addWidget(min_label)
            range_layout.addWidget(min_spin)
            range_layout.addStretch(1)
            range_layout.addWidget(max_label)
            range_layout.addWidget(max_spin)

            range_widget = QWidget()
            range_widget.setLayout(range_layout)
            range_widget.setContentsMargins(0, 0, 0, 0)
            layout.addRow(f"{name} 范围:", range_widget)
            setattr(self, f"gain_{key}_min_spin", min_spin)
            setattr(self, f"gain_{key}_max_spin", max_spin)

            # 连接信号
            min_spin.valueChanged.connect(lambda value, k=key: self.update_gain_setting(f'min_{k}', value))
            max_spin.valueChanged.connect(lambda value, k=key: self.update_gain_setting(f'max_{k}', value))

        # 添加增益参数范围控件
        add_gain_range_widgets(gain_layout, self.gain_control, "gain", "增益", -10, 10, 0.1, " dB")  # 扩大UI范围以支持-3到3的设置

        # 变化间隔
        self.gain_interval_spin = QDoubleSpinBox()
        self.gain_interval_spin.setRange(0.1, 120.0)
        self.gain_interval_spin.setValue(self.gain_control["interval_secs"])
        self.gain_interval_spin.setSuffix(" s")
        self.gain_interval_spin.setStyleSheet("""
            QDoubleSpinBox {
                padding: 6px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
            }
            QDoubleSpinBox:focus {
                border-color: #667eea;
            }
        """)
        gain_layout.addRow("变化间隔:", self.gain_interval_spin)

        # 连接信号
        self.gain_enable_checkbox.toggled.connect(self.toggle_gain_control)
        self.gain_filter_name_edit.textChanged.connect(lambda text: self.update_gain_setting('filter_name', text))
        self.gain_interval_spin.valueChanged.connect(lambda v: self.update_gain_setting("interval_secs", v))

        audio_sub_tabs.addTab(gain_tab, "🎚️ 自动增益")

        # 子Tab 6: 插件去重
        plugin_dedup_tab = QWidget()
        plugin_dedup_layout = QFormLayout(plugin_dedup_tab)
        plugin_dedup_layout.setSpacing(15)

        self.plugin_dedup_checkbox = QCheckBox("启用插件去重")
        self.plugin_dedup_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #cbd5e1;
            }
            QCheckBox::indicator:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border-color: #059669;
            }
        """)
        self.plugin_dedup_checkbox.stateChanged.connect(self.toggle_plugin_dedup_control)
        plugin_dedup_layout.addRow(self.plugin_dedup_checkbox)

        # 插件信息显示和刷新按钮
        info_widget = QWidget()
        info_layout = QHBoxLayout(info_widget)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(10)

        plugin_info_label = QLabel("以下3个VST插件将独立触发:")
        plugin_info_label.setStyleSheet("font-weight: bold; color: #374151; margin-bottom: 8px;")
        info_layout.addWidget(plugin_info_label)

        # 刷新插件状态按钮
        refresh_status_btn = QPushButton("🔄 刷新状态")
        refresh_status_btn.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
                color: black;
                border: none;
                padding: 6px 12px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 9pt;
                min-width: 80px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #4f46e5 0%, #4338ca 100%);
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background: linear-gradient(135deg, #4338ca 0%, #3730a3 100%);
                transform: translateY(0px);
            }
        """)
        refresh_status_btn.clicked.connect(self.refresh_plugin_status)
        info_layout.addWidget(refresh_status_btn)
        info_layout.addStretch()

        plugin_dedup_layout.addRow(info_widget)

        # 为每个插件创建独立的设置区域
        self.plugin_controls = []

        # 插件下载地址配置
        plugin_download_urls = [
            "https://lz.qaiu.top/parser?url=https://wwum.lanzoub.com/i598L3238hcb&pwd=fha9",  # CamelCrusher
            "https://lz.qaiu.top/parser?url=https://wwum.lanzoub.com/isVte3238hed&pwd=3prf",  # TAL-Reverb-4-64
            "https://lz.qaiu.top/parser?url=https://wwum.lanzoub.com/iVzXK3238hfe&pwd=9ypm"   # TSE_808_2.0_x64
        ]

        for i, plugin in enumerate(self.plugin_dedup_control["plugins"]):
            # 创建插件组框
            plugin_group = QGroupBox()
            plugin_group.setStyleSheet("""
                QGroupBox {
                    font-weight: bold;
                    color: #374151;
                    border: 1px solid #d1d5db;
                    border-radius: 6px;
                    margin-top: 8px;
                    padding-top: 8px;
                }
            """)

            # 创建标题区域，包含插件名称和下载按钮
            title_widget = QWidget()
            title_layout = QHBoxLayout(title_widget)
            title_layout.setContentsMargins(8, 5, 8, 5)
            title_layout.setSpacing(10)

            # 插件名称标签
            plugin_title_label = QLabel(f"{i+1}. {plugin['display_name']}")
            plugin_title_label.setStyleSheet("font-weight: bold; color: #374151; font-size: 11pt;")
            title_layout.addWidget(plugin_title_label)

            # 检查插件文件是否存在
            plugin_exists = os.path.exists(plugin['plugin_path'])

            # 下载按钮（只在插件不存在时显示）
            download_btn = QPushButton("📥 下载插件")
            download_btn.setStyleSheet("""
                QPushButton {
                    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
                    color: black;
                    border: none;
                    padding: 4px 12px;
                    border-radius: 4px;
                    font-weight: bold;
                    font-size: 9pt;
                    min-width: 80px;
                }
                QPushButton:hover {
                    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
                    transform: translateY(-1px);
                }
                QPushButton:pressed {
                    background: linear-gradient(135deg, #b45309 0%, #92400e 100%);
                    transform: translateY(0px);
                }
            """)

            # 连接下载按钮点击事件
            if i < len(plugin_download_urls):
                download_url = plugin_download_urls[i]
                download_btn.clicked.connect(lambda checked=False, url=download_url, name=plugin['display_name']: self.download_plugin(url, name))

            # 根据插件是否存在决定是否显示下载按钮
            if plugin_exists:
                download_btn.hide()
                # 添加状态标签
                status_label = QLabel("✅ 已安装")
                status_label.setStyleSheet("color: #10b981; font-weight: bold; font-size: 9pt;")
                title_layout.addWidget(status_label)
            else:
                title_layout.addWidget(download_btn)
                # 添加状态标签
                status_label = QLabel("❌ 未安装")
                status_label.setStyleSheet("color: #ef4444; font-weight: bold; font-size: 9pt;")
                title_layout.addWidget(status_label)

            title_layout.addStretch()

            # 创建主布局
            plugin_main_layout = QVBoxLayout(plugin_group)
            plugin_main_layout.setSpacing(8)
            plugin_main_layout.addWidget(title_widget)

            # 创建设置区域
            settings_widget = QWidget()
            plugin_layout = QFormLayout(settings_widget)
            plugin_layout.setSpacing(8)

            # 间隔时长设置
            interval_widget = QWidget()
            interval_layout = QHBoxLayout(interval_widget)
            interval_layout.setSpacing(8)
            interval_layout.setContentsMargins(0, 0, 0, 0)

            interval_min_spin = QDoubleSpinBox()
            interval_min_spin.setRange(1.0, 300.0)
            interval_min_spin.setValue(plugin["min_interval_secs"])
            interval_min_spin.setSuffix(" 秒")
            interval_min_spin.setSingleStep(1.0)
            interval_min_spin.setStyleSheet("""
                QDoubleSpinBox {
                    padding: 4px;
                    border: 1px solid #e2e8f0;
                    border-radius: 4px;
                    background: white;
                    min-width: 60px;
                }
                QDoubleSpinBox:focus {
                    border-color: #667eea;
                }
            """)

            interval_max_spin = QDoubleSpinBox()
            interval_max_spin.setRange(1.0, 300.0)
            interval_max_spin.setValue(plugin["max_interval_secs"])
            interval_max_spin.setSuffix(" 秒")
            interval_max_spin.setSingleStep(1.0)
            interval_max_spin.setStyleSheet(interval_min_spin.styleSheet())

            interval_layout.addWidget(QLabel("最小:"))
            interval_layout.addWidget(interval_min_spin)
            interval_layout.addWidget(QLabel("最大:"))
            interval_layout.addWidget(interval_max_spin)
            interval_layout.addStretch()

            plugin_layout.addRow("触发间隔:", interval_widget)

            # 持续时间设置
            duration_widget = QWidget()
            duration_layout = QHBoxLayout(duration_widget)
            duration_layout.setSpacing(8)
            duration_layout.setContentsMargins(0, 0, 0, 0)

            duration_min_spin = QDoubleSpinBox()
            duration_min_spin.setRange(1.0, 120.0)
            duration_min_spin.setValue(plugin["min_duration_secs"])
            duration_min_spin.setSuffix(" 秒")
            duration_min_spin.setSingleStep(1.0)
            duration_min_spin.setStyleSheet(interval_min_spin.styleSheet())

            duration_max_spin = QDoubleSpinBox()
            duration_max_spin.setRange(1.0, 120.0)
            duration_max_spin.setValue(plugin["max_duration_secs"])
            duration_max_spin.setSuffix(" 秒")
            duration_max_spin.setSingleStep(1.0)
            duration_max_spin.setStyleSheet(interval_min_spin.styleSheet())

            duration_layout.addWidget(QLabel("最小:"))
            duration_layout.addWidget(duration_min_spin)
            duration_layout.addWidget(QLabel("最大:"))
            duration_layout.addWidget(duration_max_spin)
            duration_layout.addStretch()

            plugin_layout.addRow("持续时间:", duration_widget)

            # 将设置区域添加到主布局
            plugin_main_layout.addWidget(settings_widget)

            # 保存控件引用
            plugin_controls = {
                'interval_min': interval_min_spin,
                'interval_max': interval_max_spin,
                'duration_min': duration_min_spin,
                'duration_max': duration_max_spin,
                'download_btn': download_btn,
                'status_label': status_label,
                'index': i
            }
            self.plugin_controls.append(plugin_controls)

            # 连接信号
            interval_min_spin.valueChanged.connect(lambda value, idx=i: self.update_plugin_setting(idx, 'min_interval_secs', value))
            interval_max_spin.valueChanged.connect(lambda value, idx=i: self.update_plugin_setting(idx, 'max_interval_secs', value))
            duration_min_spin.valueChanged.connect(lambda value, idx=i: self.update_plugin_setting(idx, 'min_duration_secs', value))
            duration_max_spin.valueChanged.connect(lambda value, idx=i: self.update_plugin_setting(idx, 'max_duration_secs', value))

            plugin_dedup_layout.addRow(plugin_group)

        audio_sub_tabs.addTab(plugin_dedup_tab, "🎛️ 插件去重")

        # 将主布局设置到窗口
        self.setLayout(main_layout)

        # 设置窗口样式
        self.setStyleSheet("")  # 清除所有自定义样式，使用系统默认样式

    # --- 新增：爆闪播放器Tab创建方法 --- #
    def create_flash_player_tab(self, parent_widget):
        """创建爆闪播放器Tab界面"""
        main_layout = QVBoxLayout(parent_widget)
        main_layout.setContentsMargins(10, 5, 10, 5)  # 减少边距
        main_layout.setSpacing(8)  # 减少间距



        # 创建主要内容区域
        content_widget = QWidget()
        content_layout = QHBoxLayout(content_widget)
        content_layout.setSpacing(15)  # 减少左右面板间距
        content_layout.setContentsMargins(5, 5, 5, 5)  # 减少内容边距

        # 左侧控制面板
        left_panel = self.create_flash_control_panel()
        content_layout.addWidget(left_panel, 1)

        # 右侧显示面板
        right_panel = self.create_flash_display_panel()
        content_layout.addWidget(right_panel, 1)

        main_layout.addWidget(content_widget)

        # 底部控制按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        self.flash_start_btn = QPushButton("🚀 启动几何形状轮播")
        self.flash_start_btn.setStyleSheet("""
            QPushButton {
                background: #059669;
                color: #ffffff;
                border: 3px solid #047857;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14pt;
                min-width: 180px;
                text-align: center;
            }
            QPushButton:hover {
                background: #047857;
                color: #ffffff;
                border-color: #065f46;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: #065f46;
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background: #9ca3af;
                color: #6b7280;
                border-color: #d1d5db;
            }
        """)
        self.flash_start_btn.clicked.connect(self.start_flash_player)

        self.flash_stop_btn = QPushButton("⏹️ 停止轮播")
        self.flash_stop_btn.setStyleSheet("""
            QPushButton {
                background: #dc2626;
                color: #ffffff;
                border: 3px solid #b91c1c;
                padding: 15px 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14pt;
                min-width: 150px;
                text-align: center;
            }
            QPushButton:hover {
                background: #b91c1c;
                color: #ffffff;
                border-color: #991b1b;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: #991b1b;
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background: #9ca3af;
                color: #6b7280;
                border-color: #d1d5db;
            }
        """)
        self.flash_stop_btn.clicked.connect(self.stop_flash_player)
        self.flash_stop_btn.setEnabled(False)

        button_layout.addStretch()
        button_layout.addWidget(self.flash_start_btn)
        button_layout.addWidget(self.flash_stop_btn)
        button_layout.addStretch()

        main_layout.addLayout(button_layout)

    def create_flash_control_panel(self):
        """创建爆闪播放器控制面板"""
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(12)

        # 颜色管理区域
        color_group = QGroupBox("🎨 颜色管理")
        color_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        color_layout = QVBoxLayout(color_group)

        # 颜色选择器
        color_select_layout = QHBoxLayout()
        self.random_colors_btn = QPushButton("🎲 随机添加")
        self.random_colors_btn.setStyleSheet("""
            QPushButton {
                background: #4f46e5;
                color: #ffffff;
                border: 2px solid #3730a3;
                padding: 12px 16px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 100px;
                text-align: center;
            }
            QPushButton:hover {
                background: #3730a3;
                color: #ffffff;
            }
            QPushButton:pressed {
                background: #312e81;
            }
        """)
        self.random_colors_btn.clicked.connect(self.add_random_colors)

        self.add_color_btn = QPushButton("🎨 添加颜色")
        self.add_color_btn.setStyleSheet("""
            QPushButton {
                background: #059669;
                color: #ffffff;
                border: 2px solid #047857;
                padding: 12px 16px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 100px;
                text-align: center;
            }
            QPushButton:hover {
                background: #047857;
                color: #ffffff;
            }
            QPushButton:pressed {
                background: #065f46;
            }
        """)
        self.add_color_btn.clicked.connect(self.pick_and_add_color)

        self.clear_colors_btn = QPushButton("🗑️ 清空颜色")
        self.clear_colors_btn.setStyleSheet("""
            QPushButton {
                background: #dc2626;
                color: #ffffff;
                border: 2px solid #b91c1c;
                padding: 12px 16px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 100px;
                text-align: center;
            }
            QPushButton:hover {
                background: #b91c1c;
                color: #ffffff;
            }
            QPushButton:pressed {
                background: #991b1b;
            }
        """)
        self.clear_colors_btn.clicked.connect(self.clear_colors)

        # 第一行按钮
        color_select_layout.setSpacing(8)
        color_select_layout.addWidget(self.random_colors_btn)
        color_select_layout.addWidget(self.add_color_btn)
        color_select_layout.addWidget(self.clear_colors_btn)
        color_layout.addLayout(color_select_layout)

        # 第二行按钮 - 颜色记录功能
        color_record_layout = QHBoxLayout()
        color_record_layout.setSpacing(8)

        # 保存颜色按钮
        self.save_colors_btn = QPushButton("💾 保存颜色")
        self.save_colors_btn.setStyleSheet("""
            QPushButton {
                background: #7c3aed;
                color: #ffffff;
                border: 2px solid #6d28d9;
                padding: 10px 12px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 10pt;
                min-width: 80px;
                text-align: center;
            }
            QPushButton:hover {
                background: #6d28d9;
                color: #ffffff;
            }
            QPushButton:pressed {
                background: #5b21b6;
            }
        """)
        self.save_colors_btn.clicked.connect(self.save_colors_to_file)

        # 加载颜色按钮
        self.load_colors_btn = QPushButton("📂 加载颜色")
        self.load_colors_btn.setStyleSheet("""
            QPushButton {
                background: #0891b2;
                color: #ffffff;
                border: 2px solid #0e7490;
                padding: 10px 12px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 10pt;
                min-width: 80px;
                text-align: center;
            }
            QPushButton:hover {
                background: #0e7490;
                color: #ffffff;
            }
            QPushButton:pressed {
                background: #155e75;
            }
        """)
        self.load_colors_btn.clicked.connect(self.load_colors_from_file)

        # 导出颜色按钮
        self.export_colors_btn = QPushButton("📤 导出颜色")
        self.export_colors_btn.setStyleSheet("""
            QPushButton {
                background: #ea580c;
                color: #ffffff;
                border: 2px solid #dc2626;
                padding: 10px 12px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 10pt;
                min-width: 80px;
                text-align: center;
            }
            QPushButton:hover {
                background: #dc2626;
                color: #ffffff;
            }
            QPushButton:pressed {
                background: #b91c1c;
            }
        """)
        self.export_colors_btn.clicked.connect(self.export_colors_to_text)

        color_record_layout.addWidget(self.save_colors_btn)
        color_record_layout.addWidget(self.load_colors_btn)
        color_record_layout.addWidget(self.export_colors_btn)
        color_layout.addLayout(color_record_layout)

        # 当前选中颜色显示
        self.current_color_display = QLabel("当前颜色: 未选择")
        self.current_color_display.setStyleSheet("""
            QLabel {
                padding: 8px;
                background: #f1f5f9;
                border-radius: 6px;
                border: 1px solid #cbd5e1;
                font-weight: bold;
            }
        """)
        color_layout.addWidget(self.current_color_display)

        layout.addWidget(color_group)

        # 轮播模式设置
        mode_group = QGroupBox("🔄 轮播模式")
        mode_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        mode_layout = QVBoxLayout(mode_group)

        self.mode_sequential_radio = QRadioButton("顺序轮播")
        self.mode_sequential_radio.setChecked(True)
        self.mode_random_radio = QRadioButton("随机乱序轮播")

        radio_style = """
            QRadioButton {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
                border-radius: 8px;
                border: 2px solid #cbd5e1;
            }
            QRadioButton::indicator:checked {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-color: #667eea;
            }
        """
        self.mode_sequential_radio.setStyleSheet(radio_style)
        self.mode_random_radio.setStyleSheet(radio_style)

        mode_layout.addWidget(self.mode_sequential_radio)
        mode_layout.addWidget(self.mode_random_radio)

        layout.addWidget(mode_group)

        return panel

    def create_flash_display_panel(self):
        """创建爆闪播放器显示面板"""
        panel = QWidget()
        panel.setStyleSheet("""
            QWidget {
                background: white;
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(10, 10, 10, 10)  # 进一步减少边距
        layout.setSpacing(8)  # 减少组件间距

        # 无视间隔设置
        interval_group = QGroupBox("⏱️ 无视间隔设置")
        interval_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        interval_layout = QVBoxLayout(interval_group)

        self.ignore_interval_checkbox = QCheckBox("自由无视间隔剪切")
        self.ignore_interval_checkbox.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
                font-size: 12pt;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #cbd5e1;
            }
            QCheckBox::indicator:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border-color: #059669;
            }
        """)
        interval_layout.addWidget(self.ignore_interval_checkbox)

        # 碎片数据范围 - 更紧凑的显示
        fragment_title = QLabel("📊 碎片数据范围")
        fragment_title.setAlignment(Qt.AlignCenter)
        fragment_title.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #dc2626;
                font-size: 11pt;
                margin: 3px 0px;
                background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
                padding: 4px;
                border-radius: 4px;
                border: 1px solid #dc2626;
            }
        """)
        interval_layout.addWidget(fragment_title)

        # 碎片设置容器 - 更紧凑的布局确保完全显示
        fragment_container = QWidget()
        fragment_container.setStyleSheet("""
            QWidget {
                background: linear-gradient(135deg, #fef9c3 0%, #fef08a 100%);
                border: 2px solid #eab308;
                border-radius: 8px;
                padding: 10px;
                margin: 1px;
            }
        """)
        fragment_layout = QFormLayout(fragment_container)
        fragment_layout.setSpacing(6)  # 减少间距
        fragment_layout.setContentsMargins(8, 8, 8, 8)  # 减少边距

        # 最小值设置 - 调整尺寸确保完全显示
        min_label = QLabel("最小:")
        min_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #92400e;
                font-size: 11pt;
            }
        """)
        self.fragment_min_spin = QSpinBox()
        self.fragment_min_spin.setRange(1, 1000)
        self.fragment_min_spin.setValue(50)
        self.fragment_min_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #d97706;
                border-radius: 6px;
                background: white;
                font-size: 12pt;
                font-weight: bold;
                min-width: 80px;
                max-width: 100px;
                color: #92400e;
            }
            QSpinBox:focus {
                border-color: #dc2626;
                background: #fef2f2;
            }
            QSpinBox:hover {
                background: #fffbeb;
            }
        """)
        fragment_layout.addRow(min_label, self.fragment_min_spin)

        # 最大值设置 - 调整尺寸确保完全显示
        max_label = QLabel("最大:")
        max_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #92400e;
                font-size: 11pt;
            }
        """)
        self.fragment_max_spin = QSpinBox()
        self.fragment_max_spin.setRange(1, 1000)
        self.fragment_max_spin.setValue(100)
        self.fragment_max_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #d97706;
                border-radius: 6px;
                background: white;
                font-size: 12pt;
                font-weight: bold;
                min-width: 80px;
                max-width: 100px;
                color: #92400e;
            }
            QSpinBox:focus {
                border-color: #dc2626;
                background: #fef2f2;
            }
            QSpinBox:hover {
                background: #fffbeb;
            }
        """)
        fragment_layout.addRow(max_label, self.fragment_max_spin)



        interval_layout.addWidget(fragment_container)
        layout.addWidget(interval_group)

        # 轮播设置
        carousel_group = QGroupBox("🎛️ 轮播设置【毫秒】")
        carousel_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        carousel_layout = QFormLayout(carousel_group)

        # 最小时间
        self.min_interval_spin = QSpinBox()
        self.min_interval_spin.setRange(10, 10000)
        self.min_interval_spin.setValue(self.flash_player_state["min_interval"])
        self.min_interval_spin.setSuffix(" ms")
        self.min_interval_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
                font-size: 11pt;
            }
            QSpinBox:focus {
                border-color: #667eea;
            }
        """)
        carousel_layout.addRow("最小:", self.min_interval_spin)

        # 最大时间
        self.max_interval_spin = QSpinBox()
        self.max_interval_spin.setRange(10, 10000)
        self.max_interval_spin.setValue(self.flash_player_state["max_interval"])
        self.max_interval_spin.setSuffix(" ms")
        self.max_interval_spin.setStyleSheet("""
            QSpinBox {
                padding: 8px;
                border: 2px solid #e2e8f0;
                border-radius: 6px;
                background: white;
                font-size: 11pt;
            }
            QSpinBox:focus {
                border-color: #667eea;
            }
        """)
        carousel_layout.addRow("最大:", self.max_interval_spin)

        layout.addWidget(carousel_group)

        # 已添加颜色显示区域
        colors_group = QGroupBox("🎨 已添加颜色")
        colors_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #1e293b;
                border: 2px solid #cbd5e1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background: white;
            }
        """)
        colors_layout = QVBoxLayout(colors_group)

        # 颜色列表显示
        self.colors_list_widget = QListWidget()
        self.colors_list_widget.setStyleSheet("""
            QListWidget {
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                background: #f8fafc;
                padding: 8px;
                font-size: 11pt;
            }
            QListWidget::item {
                padding: 8px;
                margin: 2px;
                border-radius: 6px;
                background: white;
                border: 1px solid #cbd5e1;
            }
            QListWidget::item:selected {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }
        """)
        self.colors_list_widget.setMaximumHeight(200)
        colors_layout.addWidget(self.colors_list_widget)

        layout.addWidget(colors_group)

        return panel

    # --- 爆闪播放器功能方法 --- #
    def add_random_colors(self, *args):
        """随机添加10个颜色"""
        import random

        # 预定义的颜色列表，确保颜色丰富多彩
        predefined_colors = [
            "#ff0000", "#00ff00", "#0000ff", "#ffff00", "#ff00ff", "#00ffff",
            "#ff8000", "#8000ff", "#ff0080", "#80ff00", "#0080ff", "#ff8080",
            "#80ff80", "#8080ff", "#ffff80", "#ff80ff", "#80ffff", "#ff4000",
            "#40ff00", "#0040ff", "#ff0040", "#4000ff", "#00ff40", "#ff8040",
            "#80ff40", "#4080ff", "#ff4080", "#8040ff", "#40ff80", "#ff2020",
            "#20ff20", "#2020ff", "#ffff20", "#ff20ff", "#20ffff", "#ff6020",
            "#60ff20", "#2060ff", "#ff2060", "#6020ff", "#20ff60", "#ff9020",
            "#90ff20", "#2090ff", "#ff2090", "#9020ff", "#20ff90", "#ffa020",
            "#a0ff20", "#20a0ff", "#ff20a0", "#a020ff", "#20ffa0", "#ffb020"
        ]

        # 随机选择10个不重复的颜色
        available_colors = [color for color in predefined_colors if color not in self.flash_player_state["colors"]]

        if len(available_colors) < 10:
            # 如果可用颜色不足10个，生成随机颜色
            for _ in range(10 - len(available_colors)):
                r = random.randint(0, 255)
                g = random.randint(0, 255)
                b = random.randint(0, 255)
                random_color = f"#{r:02x}{g:02x}{b:02x}"
                if random_color not in self.flash_player_state["colors"]:
                    available_colors.append(random_color)

        # 随机选择10个颜色
        selected_colors = random.sample(available_colors, min(10, len(available_colors)))

        # 添加到颜色列表
        added_count = 0
        for color in selected_colors:
            if color not in self.flash_player_state["colors"]:
                self.flash_player_state["colors"].append(color)
                added_count += 1

        # 更新颜色列表显示
        self.update_colors_list()

        # 显示添加结果
        QMessageBox.information(self, "随机添加完成", f"成功添加了 {added_count} 个随机颜色！")
        # print(f"随机添加了 {added_count} 个颜色: {selected_colors[:added_count]}")

        # 更新当前颜色显示
        if added_count > 0:
            last_color = selected_colors[added_count-1]
            self.current_color_display.setText(f"最新添加: {last_color}")
            self.current_color_display.setStyleSheet(f"""
                QLabel {{
                    padding: 8px;
                    background: {last_color};
                    border-radius: 6px;
                    border: 1px solid #cbd5e1;
                    color: {'white' if QColor(last_color).lightness() < 128 else 'black'};
                    font-weight: bold;
                }}
            """)

    def pick_and_add_color(self, *args):
        """弹出颜色选择器并添加颜色"""
        color = QColorDialog.getColor(QColor("#ff0000"), self, "选择要添加的颜色")
        if color.isValid():
            color_name = color.name()
            if color_name not in self.flash_player_state["colors"]:
                self.flash_player_state["colors"].append(color_name)
                self.update_colors_list()

                # 更新当前颜色显示
                self.current_color_display.setText(f"已添加: {color_name}")
                self.current_color_display.setStyleSheet(f"""
                    QLabel {{
                        padding: 8px;
                        background: {color_name};
                        border-radius: 6px;
                        border: 1px solid #cbd5e1;
                        color: {'white' if color.lightness() < 128 else 'black'};
                        font-weight: bold;
                    }}
                """)

                QMessageBox.information(self, "添加成功", f"颜色 {color_name} 已添加到列表！")
                # print(f"添加颜色: {color_name}")
            else:
                QMessageBox.information(self, "提示", "该颜色已存在！")

    def clear_colors(self, *args):
        """清空所有颜色"""
        reply = QMessageBox.question(self, "确认", "确定要清空所有颜色吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            self.flash_player_state["colors"].clear()
            self.update_colors_list()
            # print("已清空所有颜色")

    def update_colors_list(self):
        """更新颜色列表显示"""
        self.colors_list_widget.clear()
        for color in self.flash_player_state["colors"]:
            item = QListWidgetItem(f"🎨 {color}")
            item.setData(Qt.UserRole, color)
            self.colors_list_widget.addItem(item)

    def save_colors_to_file(self, *args):
        """保存颜色到文件"""
        if not self.flash_player_state["colors"]:
            QMessageBox.warning(self, "警告", "没有颜色可以保存！")
            return

        try:
            import json
            from datetime import datetime

            # 创建保存目录
            save_dir = "saved_colors"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"colors_{timestamp}.json"
            filepath = os.path.join(save_dir, filename)

            # 保存数据
            color_data = {
                "timestamp": datetime.now().isoformat(),
                "color_count": len(self.flash_player_state["colors"]),
                "colors": self.flash_player_state["colors"]
            }

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(color_data, f, indent=2, ensure_ascii=False)

            QMessageBox.information(self, "保存成功",
                f"颜色已保存到：\n{filepath}\n\n保存了 {len(self.flash_player_state['colors'])} 个颜色")
            # print(f"颜色已保存到: {filepath}")

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存颜色时出错：{e}")

    def load_colors_from_file(self, *args):
        """从文件加载颜色"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import json

            # 选择文件
            save_dir = "saved_colors"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            filepath, _ = QFileDialog.getOpenFileName(
                self, "选择颜色文件", save_dir,
                "JSON文件 (*.json);;所有文件 (*.*)"
            )

            if not filepath:
                return

            # 读取文件
            with open(filepath, 'r', encoding='utf-8') as f:
                color_data = json.load(f)

            if "colors" not in color_data:
                QMessageBox.warning(self, "格式错误", "文件格式不正确！")
                return

            # 询问是否替换
            if self.flash_player_state["colors"]:
                reply = QMessageBox.question(
                    self, "确认加载",
                    f"当前已有 {len(self.flash_player_state['colors'])} 个颜色\n"
                    f"文件包含 {len(color_data['colors'])} 个颜色\n\n是否替换？",
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply != QMessageBox.Yes:
                    return

            # 加载颜色
            self.flash_player_state["colors"] = color_data["colors"]
            self.update_colors_list()

            QMessageBox.information(self, "加载成功",
                f"成功加载了 {len(self.flash_player_state['colors'])} 个颜色！")
            # print(f"加载了 {len(self.flash_player_state['colors'])} 个颜色")

        except Exception as e:
            QMessageBox.critical(self, "加载失败", f"加载颜色时出错：{e}")

    def export_colors_to_text(self, *args):
        """导出颜色为文本"""
        if not self.flash_player_state["colors"]:
            QMessageBox.warning(self, "警告", "没有颜色可以导出！")
            return

        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime

            filepath, _ = QFileDialog.getSaveFileName(
                self, "导出颜色", f"colors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                "文本文件 (*.txt);;所有文件 (*.*)"
            )

            if not filepath:
                return

            # 生成内容
            content = []
            content.append("爆闪播放器 - 颜色导出")
            content.append("=" * 30)
            content.append(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            content.append(f"颜色数量: {len(self.flash_player_state['colors'])}")
            content.append("")
            content.append("颜色列表:")

            for i, color in enumerate(self.flash_player_state["colors"], 1):
                content.append(f"{i:2d}. {color}")

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write('\n'.join(content))

            QMessageBox.information(self, "导出成功",
                f"颜色已导出到：\n{filepath}")
            # print(f"颜色已导出到: {filepath}")

        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"导出颜色时出错：{e}")

    def save_colors_to_file(self):
        """保存颜色到文件"""
        if not self.flash_player_state["colors"]:
            QMessageBox.warning(self, "警告", "没有颜色可以保存！")
            return

        try:
            import json
            from datetime import datetime

            # 创建保存目录
            save_dir = "saved_colors"
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            # 生成文件名（包含时间戳）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"colors_{timestamp}.json"
            filepath = os.path.join(save_dir, filename)

            # 准备保存的数据
            color_data = {
                "timestamp": datetime.now().isoformat(),
                "color_count": len(self.flash_player_state["colors"]),
                "colors": self.flash_player_state["colors"],
                "settings": {
                    "min_interval": self.flash_player_state["min_interval"],
                    "max_interval": self.flash_player_state["max_interval"],
                    "fragment_min": self.flash_player_state["fragment_min"],
                    "fragment_max": self.flash_player_state["fragment_max"],
                    "play_mode": self.flash_player_state["play_mode"]
                }
            }

            # 保存到文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(color_data, f, indent=2, ensure_ascii=False)

            QMessageBox.information(self, "保存成功",
                f"颜色已保存到文件：\n{filepath}\n\n"
                f"保存了 {len(self.flash_player_state['colors'])} 个颜色")
            # print(f"颜色已保存到: {filepath}")

        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存颜色时出错：{e}")
            # print(f"保存颜色失败: {e}")

    def start_flash_player(self, *args):
        """启动爆闪播放器"""
        try:
            # 检查是否有颜色
            if not self.flash_player_state["colors"]:
                QMessageBox.warning(self, "警告", "请先添加至少一个颜色！")
                return

            # print("正在启动爆闪播放器...")

            # 安全地更新设置
            try:
                self.flash_player_state["min_interval"] = self.min_interval_spin.value()
                self.flash_player_state["max_interval"] = self.max_interval_spin.value()
                self.flash_player_state["ignore_interval"] = self.ignore_interval_checkbox.isChecked()
                self.flash_player_state["fragment_min"] = self.fragment_min_spin.value()
                self.flash_player_state["fragment_max"] = self.fragment_max_spin.value()
                self.flash_player_state["play_mode"] = "顺序轮播" if self.mode_sequential_radio.isChecked() else "随机乱序轮播"
            except Exception as e:
                print(f"更新设置时出错: {e}")
                QMessageBox.critical(self, "错误", f"更新设置时出错: {e}")
                return

            # 显示当前设置信息
            if self.flash_player_state["ignore_interval"]:
                mode_info = f"碎片模式 - 间隔: {self.flash_player_state['fragment_min']}-{self.flash_player_state['fragment_max']}ms"
            else:
                mode_info = f"正常模式 - 间隔: {self.flash_player_state['min_interval']}-{self.flash_player_state['max_interval']}ms"

            # print(f"启动设置: {mode_info}")

            # 创建并显示播放窗口
            try:
                self.create_flash_window()
                # print("播放窗口创建成功")
            except Exception as e:
                print(f"创建播放窗口时出错: {e}")
                QMessageBox.critical(self, "错误", f"创建播放窗口时出错: {e}")
                return

            # 开始播放
            self.flash_player_state["is_playing"] = True
            self.flash_player_state["current_color_index"] = 0

            # 连接定时器
            try:
                # 先断开之前的连接
                self.flash_player_state["flash_timer"].timeout.disconnect()
            except:
                pass

            self.flash_player_state["flash_timer"].timeout.connect(self.flash_next_color)

            # 立即显示第一个颜色
            self.flash_next_color()

            # 更新按钮状态
            self.flash_start_btn.setEnabled(False)
            self.flash_stop_btn.setEnabled(True)

            # print("几何形状爆闪播放器已启动")

        except Exception as e:
            print(f"启动爆闪播放器时发生错误: {e}")
            QMessageBox.critical(self, "错误", f"启动爆闪播放器时发生错误: {e}")
            # 恢复按钮状态
            self.flash_start_btn.setEnabled(True)
            self.flash_stop_btn.setEnabled(False)

    def stop_flash_player(self, *args):
        """停止爆闪播放器"""
        self.flash_player_state["is_playing"] = False
        self.flash_player_state["flash_timer"].stop()

        # 关闭播放窗口
        if self.flash_player_state["flash_window"]:
            self.flash_player_state["flash_window"].close()
            self.flash_player_state["flash_window"] = None

        # 更新按钮状态
        self.flash_start_btn.setEnabled(True)
        self.flash_stop_btn.setEnabled(False)

        # print("几何形状爆闪播放器已停止")

    def create_flash_window(self):
        """创建爆闪播放窗口"""
        try:
            # 关闭之前的窗口
            if self.flash_player_state["flash_window"]:
                try:
                    self.flash_player_state["flash_window"].close()
                except:
                    pass

            # print("正在创建几何形状播放窗口...")

            # 创建简化的播放窗口 - 9:16比例
            flash_window = QWidget()
            flash_window.setWindowTitle("几何形状爆闪播放器")
            flash_window.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)

            # 设置9:16比例的窗口大小 - 增大窗口尺寸
            window_height = 1000  # 增加到1000像素
            window_width = int(window_height * 9 / 16)  # 9:16比例，约562像素
            flash_window.resize(window_width, window_height)

            # 居中显示窗口
            screen = QApplication.primaryScreen()
            if screen:
                screen_geometry = screen.geometry()
                x = (screen_geometry.width() - window_width) // 2
                y = (screen_geometry.height() - window_height) // 2
                flash_window.move(x, y)

            # 设置窗口属性
            flash_window.current_color = "#ff0000"
            flash_window.shapes_data = []
            flash_window.is_fragment_mode = False  # 是否为碎片模式

            # 添加拖动功能的变量
            flash_window.drag_position = None

            # 添加绘制方法
            def paintEvent(event):
                try:
                    painter = QPainter(flash_window)
                    painter.setRenderHint(QPainter.Antialiasing)

                    # 设置背景色
                    painter.fillRect(flash_window.rect(), QColor("#f0f0f0"))

                    # 设置画笔和画刷
                    color = QColor(flash_window.current_color)
                    painter.setBrush(QBrush(color))
                    painter.setPen(QPen(color.darker(120), 3))

                    # 获取窗口尺寸
                    width = flash_window.width()
                    height = flash_window.height()

                    if width > 0 and height > 0:
                        if flash_window.is_fragment_mode:
                            # 碎片模式：对长方形进行随机剪切
                            import random
                            random.seed(hash(flash_window.current_color) % 1000)

                            # 绘制占满整个窗口的长方形作为基础
                            base_rect_width = width
                            base_rect_height = height
                            base_x = 0
                            base_y = 0

                            # 绘制占满整个窗口的基础长方形
                            painter.drawRect(int(base_x), int(base_y), int(base_rect_width), int(base_rect_height))

                            # 获取剪切次数（从碎片数据范围获取）
                            if hasattr(flash_window, 'fragment_cuts'):
                                cut_count = flash_window.fragment_cuts
                            else:
                                cut_count = random.randint(10, 30)  # 默认值

                            # 对长方形进行随机剪切
                            painter.setBrush(QBrush(QColor("#f0f0f0")))  # 使用背景色进行"剪切"
                            painter.setPen(QPen(QColor("#f0f0f0"), 1))

                            for i in range(cut_count):
                                # 生成多样化的随机剪切形状
                                cut_type = random.randint(0, 15)  # 16种不同的剪切类型

                                if cut_type == 0:
                                    # 随机大小的矩形剪切
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.9)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.9)
                                    cut_w = random.uniform(5, 120)
                                    cut_h = random.uniform(5, 120)
                                    painter.drawRect(int(cut_x), int(cut_y), int(cut_w), int(cut_h))

                                elif cut_type == 1:
                                    # 随机椭圆剪切
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.9)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.9)
                                    cut_w = random.uniform(10, 100)
                                    cut_h = random.uniform(10, 100)
                                    painter.drawEllipse(int(cut_x), int(cut_y), int(cut_w), int(cut_h))

                                elif cut_type == 2:
                                    # 不规则三角形剪切
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.9)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.9)

                                    triangle_points = [
                                        QPoint(int(cut_x + random.uniform(-20, 80)), int(cut_y + random.uniform(-10, 10))),
                                        QPoint(int(cut_x + random.uniform(-10, 10)), int(cut_y + random.uniform(30, 100))),
                                        QPoint(int(cut_x + random.uniform(30, 100)), int(cut_y + random.uniform(30, 100)))
                                    ]
                                    painter.drawPolygon(QPolygon(triangle_points))

                                elif cut_type == 3:
                                    # 随机多边形剪切（4-8边）
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.8)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.8)
                                    sides = random.randint(4, 8)
                                    radius = random.uniform(15, 60)

                                    polygon_points = []
                                    for j in range(sides):
                                        angle = (2 * 3.14159 * j) / sides + random.uniform(-0.5, 0.5)
                                        r = radius * random.uniform(0.5, 1.5)
                                        px = cut_x + r * random.uniform(0.7, 1.3)
                                        py = cut_y + r * random.uniform(0.7, 1.3)
                                        polygon_points.append(QPoint(int(px), int(py)))

                                    painter.drawPolygon(QPolygon(polygon_points))

                                elif cut_type == 4:
                                    # 长条形剪切（水平或垂直）
                                    if random.choice([True, False]):
                                        # 水平长条
                                        cut_x = base_x + random.uniform(0, base_rect_width * 0.2)
                                        cut_y = base_y + random.uniform(0, base_rect_height * 0.9)
                                        cut_w = random.uniform(base_rect_width * 0.3, base_rect_width * 0.8)
                                        cut_h = random.uniform(3, 25)
                                    else:
                                        # 垂直长条
                                        cut_x = base_x + random.uniform(0, base_rect_width * 0.9)
                                        cut_y = base_y + random.uniform(0, base_rect_height * 0.2)
                                        cut_w = random.uniform(3, 25)
                                        cut_h = random.uniform(base_rect_height * 0.3, base_rect_height * 0.8)

                                    painter.drawRect(int(cut_x), int(cut_y), int(cut_w), int(cut_h))

                                elif cut_type == 5:
                                    # L形剪切
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.7)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.7)
                                    size = random.uniform(30, 80)
                                    thickness = random.uniform(8, 25)

                                    # L形的两个矩形
                                    painter.drawRect(int(cut_x), int(cut_y), int(size), int(thickness))
                                    painter.drawRect(int(cut_x), int(cut_y), int(thickness), int(size))

                                elif cut_type == 6:
                                    # 锯齿形剪切
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.8)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.8)
                                    teeth_count = random.randint(3, 8)
                                    tooth_width = random.uniform(8, 20)
                                    tooth_height = random.uniform(15, 40)

                                    zigzag_points = []
                                    for j in range(teeth_count * 2 + 1):
                                        x = cut_x + j * tooth_width / 2
                                        y = cut_y + (tooth_height if j % 2 == 1 else 0)
                                        zigzag_points.append(QPoint(int(x), int(y)))

                                    # 闭合锯齿形
                                    zigzag_points.append(QPoint(int(cut_x + teeth_count * tooth_width), int(cut_y + tooth_height * 1.5)))
                                    zigzag_points.append(QPoint(int(cut_x), int(cut_y + tooth_height * 1.5)))

                                    painter.drawPolygon(QPolygon(zigzag_points))

                                elif cut_type == 7:
                                    # 弧形剪切
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.8)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.8)
                                    arc_width = random.uniform(40, 120)
                                    arc_height = random.uniform(40, 120)
                                    start_angle = random.randint(0, 360) * 16  # Qt使用1/16度
                                    span_angle = random.randint(60, 180) * 16

                                    painter.drawPie(int(cut_x), int(cut_y), int(arc_width), int(arc_height), start_angle, span_angle)

                                elif cut_type == 8:
                                    # 十字形剪切
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.8)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.8)
                                    size = random.uniform(20, 80)
                                    thickness = random.uniform(4, 20)

                                    # 十字的两个矩形
                                    painter.drawRect(int(cut_x), int(cut_y + size/2 - thickness/2), int(size), int(thickness))
                                    painter.drawRect(int(cut_x + size/2 - thickness/2), int(cut_y), int(thickness), int(size))

                                elif cut_type == 9:
                                    # 星形剪切
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.8)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.8)
                                    outer_radius = random.uniform(15, 60)
                                    inner_radius = outer_radius * random.uniform(0.3, 0.7)
                                    points_count = random.randint(4, 8)

                                    star_points = []
                                    for j in range(points_count * 2):
                                        angle = (2 * 3.14159 * j) / (points_count * 2)
                                        if j % 2 == 0:
                                            r = outer_radius
                                        else:
                                            r = inner_radius
                                        px = cut_x + r * (1 + random.uniform(-0.3, 0.3))
                                        py = cut_y + r * (1 + random.uniform(-0.3, 0.3))
                                        star_points.append(QPoint(int(px), int(py)))

                                    painter.drawPolygon(QPolygon(star_points))

                                elif cut_type == 10:
                                    # 波浪形剪切
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.6)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.8)
                                    wave_length = random.uniform(60, 150)
                                    wave_height = random.uniform(15, 40)
                                    wave_points = random.randint(8, 16)

                                    wave_polygon = []
                                    for j in range(wave_points):
                                        x = cut_x + (wave_length * j) / wave_points
                                        y = cut_y + wave_height * random.uniform(-1, 1)
                                        wave_polygon.append(QPoint(int(x), int(y)))

                                    # 闭合波浪形
                                    wave_polygon.append(QPoint(int(cut_x + wave_length), int(cut_y + wave_height * 2)))
                                    wave_polygon.append(QPoint(int(cut_x), int(cut_y + wave_height * 2)))

                                    painter.drawPolygon(QPolygon(wave_polygon))

                                elif cut_type == 11:
                                    # 螺旋形剪切
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.7)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.7)
                                    spiral_radius = random.uniform(25, 80)
                                    spiral_turns = random.uniform(1.5, 4)
                                    spiral_points = random.randint(12, 24)

                                    spiral_polygon = []
                                    for j in range(spiral_points):
                                        angle = (2 * 3.14159 * spiral_turns * j) / spiral_points
                                        r = spiral_radius * (j / spiral_points) * random.uniform(0.8, 1.2)
                                        px = cut_x + r * (1 + random.uniform(-0.2, 0.2))
                                        py = cut_y + r * (1 + random.uniform(-0.2, 0.2))
                                        spiral_polygon.append(QPoint(int(px), int(py)))

                                    painter.drawPolygon(QPolygon(spiral_polygon))

                                elif cut_type == 12:
                                    # 闪电形剪切
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.8)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.7)
                                    lightning_height = random.uniform(60, 120)
                                    lightning_width = random.uniform(15, 40)

                                    lightning_points = [
                                        QPoint(int(cut_x), int(cut_y)),
                                        QPoint(int(cut_x + lightning_width * 0.3), int(cut_y + lightning_height * 0.2)),
                                        QPoint(int(cut_x - lightning_width * 0.2), int(cut_y + lightning_height * 0.4)),
                                        QPoint(int(cut_x + lightning_width * 0.4), int(cut_y + lightning_height * 0.6)),
                                        QPoint(int(cut_x - lightning_width * 0.1), int(cut_y + lightning_height * 0.8)),
                                        QPoint(int(cut_x + lightning_width * 0.2), int(cut_y + lightning_height)),
                                        QPoint(int(cut_x + lightning_width * 0.6), int(cut_y + lightning_height * 0.7)),
                                        QPoint(int(cut_x + lightning_width * 0.8), int(cut_y + lightning_height * 0.3)),
                                        QPoint(int(cut_x + lightning_width), int(cut_y))
                                    ]
                                    painter.drawPolygon(QPolygon(lightning_points))

                                elif cut_type == 13:
                                    # 花瓣形剪切
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.8)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.8)
                                    petal_count = random.randint(4, 8)
                                    petal_radius = random.uniform(20, 50)

                                    flower_points = []
                                    for j in range(petal_count * 3):  # 每个花瓣3个点
                                        angle = (2 * 3.14159 * j) / (petal_count * 3)
                                        if j % 3 == 1:  # 花瓣尖端
                                            r = petal_radius * random.uniform(1.2, 1.8)
                                        else:  # 花瓣基部
                                            r = petal_radius * random.uniform(0.3, 0.7)
                                        px = cut_x + r * (1 + random.uniform(-0.2, 0.2))
                                        py = cut_y + r * (1 + random.uniform(-0.2, 0.2))
                                        flower_points.append(QPoint(int(px), int(py)))

                                    painter.drawPolygon(QPolygon(flower_points))

                                elif cut_type == 14:
                                    # 迷宫形剪切
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.7)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.7)
                                    maze_size = random.uniform(40, 100)
                                    wall_thickness = random.uniform(3, 8)

                                    # 创建简单的迷宫图案
                                    maze_paths = random.randint(3, 6)
                                    for j in range(maze_paths):
                                        if j % 2 == 0:
                                            # 水平路径
                                            path_x = cut_x + random.uniform(0, maze_size * 0.8)
                                            path_y = cut_y + (maze_size * j) / maze_paths
                                            path_w = random.uniform(maze_size * 0.3, maze_size * 0.7)
                                            painter.drawRect(int(path_x), int(path_y), int(path_w), int(wall_thickness))
                                        else:
                                            # 垂直路径
                                            path_x = cut_x + (maze_size * j) / maze_paths
                                            path_y = cut_y + random.uniform(0, maze_size * 0.8)
                                            path_h = random.uniform(maze_size * 0.3, maze_size * 0.7)
                                            painter.drawRect(int(path_x), int(path_y), int(wall_thickness), int(path_h))

                                else:
                                    # 完全随机形状剪切
                                    cut_x = base_x + random.uniform(0, base_rect_width * 0.8)
                                    cut_y = base_y + random.uniform(0, base_rect_height * 0.8)
                                    point_count = random.randint(3, 20)

                                    random_points = []
                                    for j in range(point_count):
                                        px = cut_x + random.uniform(-50, 120)
                                        py = cut_y + random.uniform(-50, 120)
                                        random_points.append(QPoint(int(px), int(py)))

                                    painter.drawPolygon(QPolygon(random_points))
                        else:
                            # 正常模式：绘制占满整个窗口的完整长方形
                            # 绘制占满整个窗口的长方形
                            painter.drawRect(0, 0, width, height)



                    # 添加提示文字
                    painter.setPen(QPen(QColor("white"), 2))
                    painter.drawText(20, 30, "左键拖动移动窗口")
                    painter.drawText(20, 50, "按 ESC 键或右键关闭播放器")

                    # 显示当前模式
                    mode_text = "碎片模式" if flash_window.is_fragment_mode else "正常模式"
                    painter.drawText(20, height - 20, f"当前模式: {mode_text}")

                except Exception as e:
                    print(f"绘制时出错: {e}")

            # 添加键盘事件处理
            def keyPressEvent(event):
                if event.key() == Qt.Key_Escape:
                    self.stop_flash_player()

            # 添加鼠标事件处理（支持拖动和关闭）
            def mousePressEvent(event):
                if event.button() == Qt.LeftButton:
                    # 左键按下时记录位置，用于拖动
                    flash_window.drag_position = event.globalPos() - flash_window.frameGeometry().topLeft()
                elif event.button() == Qt.RightButton:
                    # 右键关闭播放器
                    self.stop_flash_player()

            def mouseMoveEvent(event):
                if event.buttons() == Qt.LeftButton and flash_window.drag_position:
                    # 拖动窗口
                    flash_window.move(event.globalPos() - flash_window.drag_position)

            def mouseReleaseEvent(event):
                if event.button() == Qt.LeftButton:
                    # 释放拖动
                    flash_window.drag_position = None

            # 绑定事件处理方法
            flash_window.paintEvent = paintEvent
            flash_window.keyPressEvent = keyPressEvent
            flash_window.mousePressEvent = mousePressEvent
            flash_window.mouseMoveEvent = mouseMoveEvent
            flash_window.mouseReleaseEvent = mouseReleaseEvent

            # 添加更新颜色和形状的方法
            def update_color_and_shape(color, is_fragment_mode=False, fragment_cuts=15):
                flash_window.current_color = color
                flash_window.is_fragment_mode = is_fragment_mode
                flash_window.fragment_cuts = fragment_cuts  # 设置剪切次数
                flash_window.update()

            flash_window.update_color_and_shape = update_color_and_shape

            # print("窗口创建成功，准备显示9:16比例窗口...")

            # 显示窗口（不使用全屏）
            flash_window.show()
            flash_window.setFocus()
            flash_window.raise_()  # 确保窗口在最前面

            # 保存窗口引用
            self.flash_player_state["flash_window"] = flash_window

            # print("播放窗口已成功创建并显示")

        except Exception as e:
            print(f"创建播放窗口时出错: {e}")
            raise e

    def flash_next_color(self, *args):
        """切换到下一个颜色和形状"""
        if not self.flash_player_state["is_playing"] or not self.flash_player_state["colors"]:
            return

        colors = self.flash_player_state["colors"]

        # 根据播放模式选择颜色
        if self.flash_player_state["play_mode"] == "顺序轮播":
            color = colors[self.flash_player_state["current_color_index"]]
            self.flash_player_state["current_color_index"] = (self.flash_player_state["current_color_index"] + 1) % len(colors)
        else:  # 随机乱序轮播
            import random
            color = random.choice(colors)

        # 更新窗口的颜色和形状，传递碎片模式状态和剪切次数
        if self.flash_player_state["flash_window"]:
            is_fragment_mode = self.flash_player_state["ignore_interval"]
            # 根据碎片数据范围生成随机剪切次数
            if is_fragment_mode:
                import random
                fragment_min = self.flash_player_state["fragment_min"]
                fragment_max = self.flash_player_state["fragment_max"]
                fragment_cuts = random.randint(fragment_min, fragment_max)
            else:
                fragment_cuts = 0

            self.flash_player_state["flash_window"].update_color_and_shape(color, is_fragment_mode, fragment_cuts)

        # 设置下次切换时间 - 始终使用轮播设置的时间间隔
        import random
        interval = random.randint(
            self.flash_player_state["min_interval"],
            self.flash_player_state["max_interval"]
        )

        # 显示当前模式和间隔信息
        mode_text = "碎片模式" if self.flash_player_state["ignore_interval"] else "正常模式"
        if self.flash_player_state["ignore_interval"]:
            cuts_info = f", 剪切次数: {fragment_cuts}"
        else:
            cuts_info = ""
        print(f"{mode_text}切换间隔: {interval}ms{cuts_info}")

        self.flash_player_state["flash_timer"].start(interval)

    def init_flash_player(self):
        """初始化爆闪播放器"""
        # 初始化选中的颜色
        self.selected_color = None

        # 连接信号
        if hasattr(self, 'min_interval_spin'):
            self.min_interval_spin.valueChanged.connect(self.update_flash_settings)
        if hasattr(self, 'max_interval_spin'):
            self.max_interval_spin.valueChanged.connect(self.update_flash_settings)
        if hasattr(self, 'ignore_interval_checkbox'):
            self.ignore_interval_checkbox.stateChanged.connect(self.update_flash_settings)
        if hasattr(self, 'fragment_min_spin'):
            self.fragment_min_spin.valueChanged.connect(self.update_flash_settings)
        if hasattr(self, 'fragment_max_spin'):
            self.fragment_max_spin.valueChanged.connect(self.update_flash_settings)
        if hasattr(self, 'mode_sequential_radio'):
            self.mode_sequential_radio.toggled.connect(self.update_flash_settings)
        if hasattr(self, 'mode_random_radio'):
            self.mode_random_radio.toggled.connect(self.update_flash_settings)

    def update_flash_settings(self, *args):
        """更新爆闪播放器设置"""
        if hasattr(self, 'min_interval_spin'):
            self.flash_player_state["min_interval"] = self.min_interval_spin.value()
        if hasattr(self, 'max_interval_spin'):
            self.flash_player_state["max_interval"] = self.max_interval_spin.value()
        if hasattr(self, 'ignore_interval_checkbox'):
            self.flash_player_state["ignore_interval"] = self.ignore_interval_checkbox.isChecked()
        if hasattr(self, 'fragment_min_spin'):
            self.flash_player_state["fragment_min"] = self.fragment_min_spin.value()
        if hasattr(self, 'fragment_max_spin'):
            self.flash_player_state["fragment_max"] = self.fragment_max_spin.value()
        if hasattr(self, 'mode_sequential_radio'):
            self.flash_player_state["play_mode"] = "顺序轮播" if self.mode_sequential_radio.isChecked() else "随机乱序轮播"

    # --- 新增：音频播放器Tab创建方法 --- #
    def create_audio_player_tab(self, parent_widget):
        """创建音频播放器Tab界面"""
        # 创建滚动区域
        scroll_area = QScrollArea(parent_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setMinimumHeight(500)  # 减少最小高度
        scroll_area.setMaximumHeight(700)  # 设置最大高度

        # 创建内容widget
        content_widget = QWidget()
        scroll_area.setWidget(content_widget)

        # 主布局
        main_layout = QVBoxLayout(parent_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)  # 减少边距
        main_layout.addWidget(scroll_area)

        # 内容布局
        content_layout = QVBoxLayout(content_widget)
        content_layout.setSpacing(8)  # 进一步减少间距
        content_layout.setContentsMargins(10, 10, 10, 10)  # 进一步减少内边距

        # 标题
        title = QLabel("🎵 智能化融合·高阶功能")
        title.setStyleSheet("""
            QLabel {
                font-size: 13pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 8px;
                background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
                border-radius: 6px;
                border: 1px solid #bdc3c7;
                margin-bottom: 5px;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        title.setMaximumHeight(40)  # 进一步限制标题高度
        content_layout.addWidget(title)

        # 创建功能控制区域（使用表单布局）
        form_layout = QFormLayout()
        form_layout.setSpacing(8)  # 进一步减少间距
        form_layout.setLabelAlignment(Qt.AlignLeft)
        form_layout.setFormAlignment(Qt.AlignLeft | Qt.AlignTop)
        form_layout.setVerticalSpacing(8)  # 减少垂直间距
        form_layout.setHorizontalSpacing(8)  # 减少水平间距

        # 输出设备选择
        device_label = QLabel("🔊 输出设备")
        device_label.setStyleSheet(self.get_label_style())

        device_widget = QWidget()
        device_layout = QHBoxLayout(device_widget)
        device_layout.setContentsMargins(0, 0, 0, 0)
        device_layout.setSpacing(10)

        self.output_device_combo = QComboBox()
        self.populate_audio_devices()  # 获取系统音频设备
        self.output_device_combo.setStyleSheet(self.get_combo_style())
        self.output_device_combo.setMinimumWidth(300)
        self.output_device_combo.currentTextChanged.connect(self.on_audio_device_changed)

        refresh_device_btn = QPushButton("🔄 刷新")
        refresh_device_btn.setStyleSheet(self.get_button_style("#17a2b8"))
        refresh_device_btn.clicked.connect(self.refresh_audio_devices)
        refresh_device_btn.setMaximumWidth(80)  # 从60调整到80
        refresh_device_btn.setMinimumHeight(28)  # 减小高度
        refresh_device_btn.setMaximumHeight(32)  # 限制最大高度
        refresh_device_btn.setToolTip("重新检测音频输出设备")

        device_layout.addWidget(self.output_device_combo)
        device_layout.addWidget(refresh_device_btn)
        device_layout.addStretch()

        form_layout.addRow(device_label, device_widget)

        # 音频文件夹选择
        folder_label = QLabel("🎵 音频文件夹")
        folder_label.setStyleSheet(self.get_label_style())

        folder_widget = QWidget()
        folder_layout = QHBoxLayout(folder_widget)
        folder_layout.setContentsMargins(0, 0, 0, 0)
        folder_layout.setSpacing(10)

        self.audio_folder_combo = QComboBox()
        self.audio_folder_combo.addItem("重音频/[3] 音频去重素材")
        self.audio_folder_combo.setStyleSheet(self.get_combo_style())
        self.audio_folder_combo.setMinimumWidth(350)  # 从300增加到350
        self.audio_folder_combo.setMaximumWidth(400)  # 添加最大宽度限制

        select_folder_btn = QPushButton("📁 选择文件夹")
        select_folder_btn.setStyleSheet(self.get_button_style("#17a2b8"))
        select_folder_btn.clicked.connect(self._select_audio_folder_safe)
        select_folder_btn.setMaximumWidth(120)  # 从120调整到100
        select_folder_btn.setMinimumHeight(28)  # 减小高度
        select_folder_btn.setMaximumHeight(32)  # 限制最大高度

        folder_layout.addWidget(self.audio_folder_combo)
        folder_layout.addWidget(select_folder_btn)
        folder_layout.addStretch()

        form_layout.addRow(folder_label, folder_widget)

        # 播放模式
        mode_label = QLabel("🔄 播放模式")
        mode_label.setStyleSheet(self.get_label_style())
        self.play_mode_combo = QComboBox()
        self.play_mode_combo.addItems(["完整播放", "片段播放"])
        self.play_mode_combo.setStyleSheet(self.get_combo_style())
        self.play_mode_combo.currentTextChanged.connect(self.on_play_mode_changed)
        form_layout.addRow(mode_label, self.play_mode_combo)

        # 间隔时间控制
        interval_label = QLabel("⏰ 间隔时间")
        interval_label.setStyleSheet(self.get_label_style())

        interval_widget = QWidget()
        interval_layout = QHBoxLayout(interval_widget)
        interval_layout.setContentsMargins(0, 0, 0, 0)
        interval_layout.setSpacing(10)

        self.interval_min_spin = QDoubleSpinBox()
        self.interval_min_spin.setRange(1.0, 300.0)
        self.interval_min_spin.setValue(30.0)
        self.interval_min_spin.setSuffix(" 秒")
        self.interval_min_spin.setStyleSheet(self.get_spinbox_style())
        self.interval_min_spin.setMinimumWidth(120)
        self.interval_min_spin.setMaximumWidth(150)

        to_label = QLabel("到")
        to_label.setStyleSheet("color: #666; font-weight: bold;")

        self.interval_max_spin = QDoubleSpinBox()
        self.interval_max_spin.setRange(1.0, 300.0)
        self.interval_max_spin.setValue(40.0)
        self.interval_max_spin.setSuffix(" 秒")
        self.interval_max_spin.setStyleSheet(self.get_spinbox_style())
        self.interval_max_spin.setMinimumWidth(120)
        self.interval_max_spin.setMaximumWidth(150)

        interval_layout.addWidget(self.interval_min_spin)
        interval_layout.addWidget(to_label)
        interval_layout.addWidget(self.interval_max_spin)
        interval_layout.addStretch()

        form_layout.addRow(interval_label, interval_widget)

        # 片段时长
        self.duration_label = QLabel("⏱️ 片段时长")
        self.duration_label.setStyleSheet(self.get_label_style())

        self.segment_duration_spin = QSpinBox()
        self.segment_duration_spin.setRange(1, 60)
        self.segment_duration_spin.setValue(5)
        self.segment_duration_spin.setSuffix(" 秒")
        self.segment_duration_spin.setStyleSheet(self.get_spinbox_style())
        self.segment_duration_spin.setMinimumWidth(120)
        self.segment_duration_spin.setMaximumWidth(150)

        # 默认禁用片段时长控件（只有选择片段播放时才启用）
        self.duration_label.setEnabled(False)
        self.segment_duration_spin.setEnabled(False)

        form_layout.addRow(self.duration_label, self.segment_duration_spin)

        # 音量范围控制
        volume_label = QLabel("🔊 音量范围")
        volume_label.setStyleSheet(self.get_label_style())

        volume_widget = QWidget()
        volume_layout = QHBoxLayout(volume_widget)
        volume_layout.setContentsMargins(0, 0, 0, 0)
        volume_layout.setSpacing(10)

        self.volume1_min_spin = QDoubleSpinBox()
        self.volume1_min_spin.setRange(0.0, 2.0)
        self.volume1_min_spin.setValue(0.20)
        self.volume1_min_spin.setSingleStep(0.01)
        self.volume1_min_spin.setDecimals(2)
        self.volume1_min_spin.setStyleSheet(self.get_spinbox_style())
        self.volume1_min_spin.setMinimumWidth(100)
        self.volume1_min_spin.setMaximumWidth(120)

        volume_to_label = QLabel("到")
        volume_to_label.setStyleSheet("color: #666; font-weight: bold;")

        self.volume1_max_spin = QDoubleSpinBox()
        self.volume1_max_spin.setRange(0.0, 2.0)
        self.volume1_max_spin.setValue(0.50)
        self.volume1_max_spin.setSingleStep(0.01)
        self.volume1_max_spin.setDecimals(2)
        self.volume1_max_spin.setStyleSheet(self.get_spinbox_style())
        self.volume1_max_spin.setMinimumWidth(100)
        self.volume1_max_spin.setMaximumWidth(120)

        volume_layout.addWidget(self.volume1_min_spin)
        volume_layout.addWidget(volume_to_label)
        volume_layout.addWidget(self.volume1_max_spin)
        volume_layout.addStretch()

        form_layout.addRow(volume_label, volume_widget)

        # 增益范围
        gain_label = QLabel("🎚️ 增益范围")
        gain_label.setStyleSheet(self.get_label_style())

        gain_widget = QWidget()
        gain_layout = QHBoxLayout(gain_widget)
        gain_layout.setContentsMargins(0, 0, 0, 0)
        gain_layout.setSpacing(10)

        self.gain_min_spin = QDoubleSpinBox()
        self.gain_min_spin.setRange(0.0, 2.0)
        self.gain_min_spin.setValue(0.90)
        self.gain_min_spin.setSingleStep(0.01)
        self.gain_min_spin.setDecimals(2)
        self.gain_min_spin.setStyleSheet(self.get_spinbox_style())
        self.gain_min_spin.setMinimumWidth(100)
        self.gain_min_spin.setMaximumWidth(120)

        gain_to_label = QLabel("到")
        gain_to_label.setStyleSheet("color: #666; font-weight: bold;")

        self.gain_max_spin = QDoubleSpinBox()
        self.gain_max_spin.setRange(0.0, 2.0)
        self.gain_max_spin.setValue(1.10)
        self.gain_max_spin.setSingleStep(0.01)
        self.gain_max_spin.setDecimals(2)
        self.gain_max_spin.setStyleSheet(self.get_spinbox_style())
        self.gain_max_spin.setMinimumWidth(100)
        self.gain_max_spin.setMaximumWidth(120)

        gain_layout.addWidget(self.gain_min_spin)
        gain_layout.addWidget(gain_to_label)
        gain_layout.addWidget(self.gain_max_spin)
        gain_layout.addStretch()

        form_layout.addRow(gain_label, gain_widget)

        # 音调范围
        pitch_label = QLabel("🎵 音调范围")
        pitch_label.setStyleSheet(self.get_label_style())

        pitch_widget = QWidget()
        pitch_layout = QHBoxLayout(pitch_widget)
        pitch_layout.setContentsMargins(0, 0, 0, 0)
        pitch_layout.setSpacing(10)

        self.pitch_min_spin = QDoubleSpinBox()
        self.pitch_min_spin.setRange(0.0, 2.0)
        self.pitch_min_spin.setValue(1.00)
        self.pitch_min_spin.setSingleStep(0.01)
        self.pitch_min_spin.setDecimals(2)
        self.pitch_min_spin.setStyleSheet(self.get_spinbox_style())
        self.pitch_min_spin.setMinimumWidth(100)
        self.pitch_min_spin.setMaximumWidth(120)

        pitch_to_label = QLabel("到")
        pitch_to_label.setStyleSheet("color: #666; font-weight: bold;")

        self.pitch_max_spin = QDoubleSpinBox()
        self.pitch_max_spin.setRange(0.0, 2.0)
        self.pitch_max_spin.setValue(1.10)
        self.pitch_max_spin.setSingleStep(0.01)
        self.pitch_max_spin.setDecimals(2)
        self.pitch_max_spin.setStyleSheet(self.get_spinbox_style())
        self.pitch_max_spin.setMinimumWidth(100)
        self.pitch_max_spin.setMaximumWidth(120)

        pitch_layout.addWidget(self.pitch_min_spin)
        pitch_layout.addWidget(pitch_to_label)
        pitch_layout.addWidget(self.pitch_max_spin)
        pitch_layout.addStretch()

        form_layout.addRow(pitch_label, pitch_widget)

        # 添加表单布局到内容布局
        content_layout.addLayout(form_layout)

        # 控制按钮区域
        button_widget = QWidget()
        button_layout = QHBoxLayout(button_widget)
        button_layout.setContentsMargins(0, 15, 0, 5)  # 减少边距
        button_layout.setSpacing(15)  # 适中的按钮间距

        self.start_audio_btn = QPushButton("🎵 开始播放")
        self.start_audio_btn.setStyleSheet(self.get_modern_button_style("#4CAF50"))
        self.start_audio_btn.clicked.connect(self.start_audio_playback)
        self.start_audio_btn.setMinimumHeight(32)  # 减小高度从40到32
        self.start_audio_btn.setMinimumWidth(90)   # 减小宽度从110到90

        self.stop_audio_btn = QPushButton("⏹️ 停止播放")
        self.stop_audio_btn.setStyleSheet(self.get_modern_button_style("#FF5722"))
        self.stop_audio_btn.clicked.connect(self.stop_audio_playback)
        self.stop_audio_btn.setEnabled(False)
        self.stop_audio_btn.setMinimumHeight(32)   # 减小高度从40到32
        self.stop_audio_btn.setMinimumWidth(90)    # 减小宽度从110到90

        # 居中显示按钮
        button_layout.addStretch()
        button_layout.addWidget(self.start_audio_btn)
        button_layout.addWidget(self.stop_audio_btn)
        button_layout.addStretch()

        content_layout.addWidget(button_widget)

        # 状态显示区域
        self.audio_status_label = QLabel("🎵 音频播放器就绪")
        self.audio_status_label.setStyleSheet("""
            QLabel {
                font-size: 11pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 8px 12px;
                background: #F5F5F5;
                border: 1px solid #E0E0E0;
                border-radius: 4px;
                margin-top: 8px;
                min-width: 600px;
            }
        """)
        self.audio_status_label.setAlignment(Qt.AlignCenter)
        self.audio_status_label.setMaximumHeight(50)  # 限制高度
        self.audio_status_label.setMinimumWidth(600)  # 设置最小宽度
        content_layout.addWidget(self.audio_status_label)

        # 添加弹性空间
        content_layout.addStretch()

        # 初始化音频播放器状态
        self.audio_folder_path = ""
        self.audio_files_list = []
        self.current_audio_index = 0
        self.audio_timer = QTimer()
        self.audio_timer.timeout.connect(self.play_next_audio_segment)
        self.is_audio_playing = False

        # 初始化播放模式状态
        self.on_play_mode_changed("完整播放")  # 设置初始状态

        # 加载保存的音频文件夹路径
        self.load_saved_audio_folder()

        # 加载保存的音频输出设备
        self.load_saved_audio_device()

        # 🔧 新增：加载音频播放器参数
        self.load_audio_player_settings()

        # 🔧 新增：连接音频播放器参数变化信号
        self.connect_audio_player_signals()

    def on_play_mode_changed(self, mode):
        """播放模式改变时的处理"""
        if mode == "片段播放":
            # 启用片段时长控件
            self.duration_label.setEnabled(True)
            self.segment_duration_spin.setEnabled(True)
            self.duration_label.setStyleSheet(self.get_label_style())
            # print("切换到片段播放模式，启用片段时长设置")
        else:
            # 禁用片段时长控件
            self.duration_label.setEnabled(False)
            self.segment_duration_spin.setEnabled(False)
            # 设置禁用状态的样式
            self.duration_label.setStyleSheet("""
                QLabel {
                    font-size: 12pt;
                    font-weight: bold;
                    color: #95a5a6;
                    padding: 5px;
                }
            """)
            # print("切换到完整播放模式，禁用片段时长设置")

        # 🔧 新增：自动保存播放模式设置
        self.save_audio_player_settings()

    def load_saved_audio_folder(self):
        """加载保存的音频文件夹路径"""
        try:
            settings = QSettings("OBS去重工具", "AudioPlayer")
            saved_folder = settings.value("last_audio_folder", "")

            if saved_folder and os.path.exists(saved_folder):
                self.audio_folder_path = saved_folder
                folder_name = os.path.basename(saved_folder)

                # 更新界面显示
                self.audio_folder_combo.clear()
                self.audio_folder_combo.addItem(f"📁 {folder_name}")

                # 扫描音频文件
                self.scan_audio_files()

                # print(f"✅ 已加载保存的音频文件夹: {folder_name}")
                self.update_audio_status(f"📁 已加载: {folder_name}")
            else:
                # print("ℹ️ 没有保存的音频文件夹路径或路径不存在")
                pass

        except Exception as e:
            print(f"❌ 加载保存的音频文件夹时出错: {e}")

    def save_audio_folder_path(self, folder_path):
        """保存音频文件夹路径"""
        try:
            settings = QSettings("OBS去重工具", "AudioPlayer")
            settings.setValue("last_audio_folder", folder_path)
            settings.sync()
            # print(f"✅ 已保存音频文件夹路径: {os.path.basename(folder_path)}")
        except Exception as e:
            print(f"❌ 保存音频文件夹路径时出错: {e}")

    def load_saved_audio_device(self):
        """加载保存的音频输出设备"""
        try:
            settings = QSettings("OBS去重工具", "AudioPlayer")
            saved_device_name = settings.value("last_audio_device", "")

            if saved_device_name:
                # 在设备列表中查找匹配的设备
                for i in range(self.output_device_combo.count()):
                    item_text = self.output_device_combo.itemText(i)
                    # 检查设备名称是否匹配（去掉图标前缀）
                    if saved_device_name in item_text:
                        self.output_device_combo.setCurrentIndex(i)
                        # print(f"✅ 已加载保存的音频输出设备: {saved_device_name}")
                        self.update_audio_status(f"🔊 已加载设备: {saved_device_name}")

                        # 设置音频输出设备
                        selected_device_data = self.output_device_combo.currentData()
                        if selected_device_data and hasattr(selected_device_data, 'deviceName'):
                            self.set_audio_output_device(selected_device_data)

                        return

                # print(f"⚠️ 保存的音频设备 '{saved_device_name}' 未找到，可能已被移除")
            else:
                # print("ℹ️ 没有保存的音频输出设备")
                pass

        except Exception as e:
            print(f"❌ 加载保存的音频输出设备时出错: {e}")

    def save_audio_device(self, device_name):
        """保存音频输出设备"""
        try:
            settings = QSettings("OBS去重工具", "AudioPlayer")
            # 保存设备的实际名称（去掉图标前缀）
            clean_device_name = device_name.replace("🔊 ", "").replace("🎵 ", "").replace(" (默认)", "")
            settings.setValue("last_audio_device", clean_device_name)
            settings.sync()
            # print(f"✅ 已保存音频输出设备: {clean_device_name}")
        except Exception as e:
            print(f"❌ 保存音频输出设备时出错: {e}")

    def load_audio_player_settings(self):
        """加载音频播放器参数设置"""
        try:
            settings = QSettings("OBS去重工具", "AudioPlayer")

            # 加载音量范围
            volume_min = settings.value("volume_min", 0.2, type=float)
            volume_max = settings.value("volume_max", 0.8, type=float)
            self.volume1_min_spin.setValue(volume_min)
            self.volume1_max_spin.setValue(volume_max)

            # 加载增益范围
            gain_min = settings.value("gain_min", -2.0, type=float)
            gain_max = settings.value("gain_max", 2.0, type=float)
            self.gain_min_spin.setValue(gain_min)
            self.gain_max_spin.setValue(gain_max)

            # 加载音调范围
            pitch_min = settings.value("pitch_min", 0.9, type=float)
            pitch_max = settings.value("pitch_max", 1.1, type=float)
            self.pitch_min_spin.setValue(pitch_min)
            self.pitch_max_spin.setValue(pitch_max)

            # 加载间隔时间范围
            interval_min = settings.value("interval_min", 1.0, type=float)
            interval_max = settings.value("interval_max", 5.0, type=float)
            self.interval_min_spin.setValue(interval_min)
            self.interval_max_spin.setValue(interval_max)

            # 加载播放模式
            play_mode = settings.value("play_mode", "完整播放", type=str)
            index = self.play_mode_combo.findText(play_mode)
            if index >= 0:
                self.play_mode_combo.setCurrentIndex(index)

            # 加载片段播放时长
            segment_duration = settings.value("segment_duration", 3, type=int)
            self.segment_duration_spin.setValue(segment_duration)

            # print("✅ 音频播放器参数已加载")

        except Exception as e:
            print(f"❌ 加载音频播放器参数时出错: {e}")

    def save_audio_player_settings(self, *args):
        """保存音频播放器参数设置"""
        try:
            settings = QSettings("OBS去重工具", "AudioPlayer")

            # 保存音量范围
            settings.setValue("volume_min", self.volume1_min_spin.value())
            settings.setValue("volume_max", self.volume1_max_spin.value())

            # 保存增益范围
            settings.setValue("gain_min", self.gain_min_spin.value())
            settings.setValue("gain_max", self.gain_max_spin.value())

            # 保存音调范围
            settings.setValue("pitch_min", self.pitch_min_spin.value())
            settings.setValue("pitch_max", self.pitch_max_spin.value())

            # 保存间隔时间范围
            settings.setValue("interval_min", self.interval_min_spin.value())
            settings.setValue("interval_max", self.interval_max_spin.value())

            # 保存播放模式
            settings.setValue("play_mode", self.play_mode_combo.currentText())

            # 保存片段播放时长
            settings.setValue("segment_duration", self.segment_duration_spin.value())

            settings.sync()
            # print("✅ 音频播放器参数已保存")

        except Exception as e:
            print(f"❌ 保存音频播放器参数时出错: {e}")

    def connect_audio_player_signals(self):
        """连接音频播放器参数变化信号"""
        try:
            # 连接音量范围变化信号
            self.volume1_min_spin.valueChanged.connect(self.save_audio_player_settings)
            self.volume1_max_spin.valueChanged.connect(self.save_audio_player_settings)

            # 连接增益范围变化信号
            self.gain_min_spin.valueChanged.connect(self.save_audio_player_settings)
            self.gain_max_spin.valueChanged.connect(self.save_audio_player_settings)

            # 连接音调范围变化信号
            self.pitch_min_spin.valueChanged.connect(self.save_audio_player_settings)
            self.pitch_max_spin.valueChanged.connect(self.save_audio_player_settings)

            # 连接间隔时间范围变化信号
            self.interval_min_spin.valueChanged.connect(self.save_audio_player_settings)
            self.interval_max_spin.valueChanged.connect(self.save_audio_player_settings)

            # 连接片段播放时长变化信号
            self.segment_duration_spin.valueChanged.connect(self.save_audio_player_settings)

            # print("✅ 音频播放器参数变化信号已连接")

        except Exception as e:
            print(f"❌ 连接音频播放器参数信号时出错: {e}")

    def populate_audio_devices(self):
        """获取系统音频输出设备"""
        try:
            # 清空现有选项
            self.output_device_combo.clear()

            # print("开始检测音频输出设备...")
            devices_found = []

            # 方法1: 尝试使用PyQt5的QAudioDeviceInfo
            try:
                # print("尝试Qt音频设备API...")
                qt_devices = self.get_qt_audio_devices()

                if qt_devices:
                    for device_name, device_obj in qt_devices:
                        self.output_device_combo.addItem(device_name, device_obj)
                        devices_found.append(device_name)
                    # print(f"✅ Qt API找到 {len(qt_devices)} 个音频输出设备")
                    self.update_audio_status(f"✅ 检测到 {len(qt_devices)} 个音频输出设备")
                    return
            except Exception as e:
                print(f"Qt音频设备获取失败: {e}")

            # 方法2: 如果Qt方法失败，尝试Windows方法
            if platform.system() == "Windows":
                try:
                    # print("Qt方法未找到设备，尝试Windows API...")
                    windows_devices = self.get_windows_audio_devices()
                    if windows_devices:
                        for device_name in windows_devices:
                            display_name = f"🔊 {device_name}"
                            self.output_device_combo.addItem(display_name, device_name)
                            devices_found.append(display_name)
                        # print(f"✅ Windows API找到 {len(windows_devices)} 个音频输出设备")
                        self.update_audio_status(f"✅ 检测到 {len(windows_devices)} 个音频输出设备")
                        return
                except Exception as e:
                    print(f"Windows音频设备获取失败: {e}")

            # 如果都失败，添加默认选项
            # print("⚠️ 所有方法都未找到设备，使用备用列表")
            self.add_fallback_devices()

        except Exception as e:
            print(f"获取音频设备时出错: {e}")
            self.add_fallback_devices()

    def refresh_audio_devices(self, *args):
        """刷新音频设备列表"""
        # print("用户请求刷新音频设备列表")
        self.update_audio_status("🔄 正在刷新音频设备...")
        self.populate_audio_devices()
        # print("音频设备列表刷新完成")

    def update_audio_status(self, message):
        """更新音频状态显示"""
        if hasattr(self, 'audio_status_label'):
            self.audio_status_label.setText(message)

    def on_audio_device_changed(self, device_name):
        """音频设备选择改变时的处理"""
        if device_name:
            selected_device_data = self.output_device_combo.currentData()

            # 保存选择的设备
            self.save_audio_device(device_name)

            if selected_device_data and hasattr(selected_device_data, 'deviceName') and not selected_device_data.isNull():
                # 预设置音频输出设备（不播放音频）
                success = self.set_audio_output_device(selected_device_data)
                if success:
                    self.update_audio_status(f"🔊 已切换到: {device_name}")
                    # print(f"✅ 音频输出设备已切换到: {device_name}")
                else:
                    self.update_audio_status(f"⚠️ 无法切换到: {device_name}")
                    # print(f"⚠️ 无法切换到音频设备: {device_name}")
            else:
                self.update_audio_status(f"ℹ️ 已选择: {device_name}")
                # print(f"ℹ️ 选择了设备: {device_name} (将使用系统默认输出)")

    def get_qt_audio_devices(self):
        """使用Qt API获取音频设备"""
        devices = []
        device_names_seen = set()  # 用于去重

        try:
            # 获取默认音频输出设备
            default_device = QAudioDeviceInfo.defaultOutputDevice()
            default_device_name = ""

            # 获取所有可用的音频输出设备 - 修复API调用
            available_devices = QAudioDeviceInfo.availableDevices(QAudio.AudioOutput)

            # 添加默认设备（如果存在）
            if not default_device.isNull():
                default_device_name = default_device.deviceName()
                default_name = f"🔊 {default_device_name} (默认)"
                devices.append((default_name, default_device))
                device_names_seen.add(default_device_name)
                # print(f"添加默认设备: {default_device_name}")

            # 添加其他可用设备，使用设备名称去重
            for device in available_devices:
                if not device.isNull():
                    device_name = device.deviceName()
                    # 使用设备名称而不是对象比较来去重
                    if device_name not in device_names_seen:
                        display_name = f"🎵 {device_name}"
                        devices.append((display_name, device))
                        device_names_seen.add(device_name)
                        # print(f"添加设备: {device_name}")
                    else:
                        # print(f"跳过重复设备: {device_name}")
                        pass

            # print(f"Qt API共找到 {len(devices)} 个不重复的音频设备")

        except Exception as e:
            print(f"Qt音频设备获取失败: {e}")

        return devices

    def get_windows_audio_devices(self):
        """使用Windows PowerShell获取音频设备"""
        devices = []
        try:
            # 使用PowerShell命令获取音频设备
            powershell_cmd = 'Get-WmiObject -Class Win32_SoundDevice | Where-Object {$_.Status -eq "OK"} | Select-Object Name | Format-Table -HideTableHeaders'

            # 尝试不同的PowerShell路径
            powershell_paths = [
                "powershell.exe",
                "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe",
                "C:\\Windows\\SysWOW64\\WindowsPowerShell\\v1.0\\powershell.exe"
            ]

            result = None
            for ps_path in powershell_paths:
                try:
                    result = subprocess.run(
                        [ps_path, "-Command", powershell_cmd],
                        capture_output=True,
                        text=True,
                        timeout=5  # 减少超时时间
                    )
                    if result.returncode == 0:
                        print(f"PowerShell路径成功: {ps_path}")
                        break
                except (FileNotFoundError, subprocess.TimeoutExpired, subprocess.SubprocessError):
                    continue

            if result is None:
                raise FileNotFoundError("找不到PowerShell")

            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if line and not line.startswith('-') and line != '':
                        # 清理设备名称
                        device_name = line.split()[0] if line.split() else line
                        if device_name and len(device_name) > 3:
                            devices.append(device_name)

            # 如果上面的方法失败，尝试另一种方法
            if not devices:
                devices = self.get_windows_audio_devices_alternative()

        except Exception as e:
            print(f"Windows音频设备获取失败: {e}")

        return devices

    def get_windows_audio_devices_alternative(self):
        """使用替代方法获取Windows音频设备"""
        devices = []
        try:
            # 尝试不同的WMIC路径
            wmic_paths = [
                "wmic.exe",
                "C:\\Windows\\System32\\wbem\\wmic.exe",
                "C:\\Windows\\SysWOW64\\wbem\\wmic.exe"
            ]

            result = None
            for wmic_path in wmic_paths:
                try:
                    result = subprocess.run(
                        [wmic_path, "sounddev", "get", "name"],
                        capture_output=True,
                        text=True,
                        timeout=5  # 减少超时时间
                    )
                    if result.returncode == 0:
                        print(f"WMIC路径成功: {wmic_path}")
                        break
                except (FileNotFoundError, subprocess.TimeoutExpired, subprocess.SubprocessError):
                    continue

            if result is None:
                raise FileNotFoundError("找不到WMIC")

            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                for line in lines[1:]:  # 跳过标题行
                    line = line.strip()
                    if line and line != "Name":
                        devices.append(line)

        except Exception as e:
            print(f"替代Windows音频设备获取失败: {e}")

        return devices

    def add_fallback_devices(self):
        """添加备用音频设备选项"""
        fallback_devices = [
            "🔊 系统默认音频设备",
            "🎵 Microsoft Sound Mapper - Output",
            "🎵 扬声器 (Realtek High Definition Audio)",
            "🎵 耳机 (Realtek High Definition Audio)",
            "🎵 HDMI输出设备",
            "🎵 USB音频设备",
            "🎵 蓝牙音频设备"
        ]

        for device in fallback_devices:
            self.output_device_combo.addItem(device, None)

        print(f"使用备用音频设备列表，共 {len(fallback_devices)} 个选项")
        self.update_audio_status(f"⚠️ 使用备用设备列表 ({len(fallback_devices)} 个选项)")

    def create_audio_output_for_device(self, device_info):
        """为指定设备创建音频输出对象"""
        try:
            if device_info and not device_info.isNull():
                # 获取设备支持的音频格式
                format = device_info.preferredFormat()

                # 创建音频输出对象
                audio_output = QAudioOutput(device_info, format)

                print(f"为设备 '{device_info.deviceName()}' 创建音频输出成功")
                print(f"  - 采样率: {format.sampleRate()} Hz")
                print(f"  - 声道数: {format.channelCount()}")
                print(f"  - 采样大小: {format.sampleSize()} bits")

                return audio_output, format
            else:
                print("设备信息无效，使用默认音频输出")
                return None, None

        except Exception as e:
            print(f"创建音频输出时出错: {e}")
            return None, None

    def set_audio_output_device(self, device_info):
        """设置音频输出设备"""
        try:
            # 停止当前的音频输出
            if self.current_audio_output:
                self.current_audio_output.stop()
                self.current_audio_output = None

            # 创建新的音频输出
            if device_info and not device_info.isNull():
                audio_output, format = self.create_audio_output_for_device(device_info)
                if audio_output:
                    self.current_audio_output = audio_output
                    self.selected_audio_device = device_info

                    # 设置音量
                    volume_linear = self.volume / 100.0  # 转换为0.0-1.0范围
                    self.current_audio_output.setVolume(volume_linear)

                    # print(f"✅ 音频输出设备设置成功: {device_info.deviceName()}")
                    return True

            # print("⚠️ 使用系统默认音频输出设备")
            return False

        except Exception as e:
            print(f"❌ 设置音频输出设备时出错: {e}")
            return False

    def apply_audio_gain(self, audio_data, gain_db):
        """应用音频增益"""
        try:
            # 将dB转换为线性增益
            gain_linear = 10 ** (gain_db / 20.0)

            # 应用增益
            processed_audio = audio_data * gain_linear

            # 防止音频削波
            max_val = np.max(np.abs(processed_audio))
            if max_val > 1.0:
                processed_audio = processed_audio / max_val
                # print(f"⚠️ 音频增益过大，已自动限制防止削波")
                pass

            return processed_audio

        except Exception as e:
            print(f"❌ 应用音频增益时出错: {e}")
            return audio_data

    def apply_pitch_shift(self, audio_data, sample_rate, pitch_factor):
        """应用音调变化（简单的重采样方法）"""
        try:
            if abs(pitch_factor - 1.0) < 0.01:  # 如果音调变化很小，跳过处理
                return audio_data

            # 使用简单的重采样来改变音调
            # 注意：这会同时改变播放速度，更高级的算法需要额外的库
            original_length = len(audio_data)
            new_length = int(original_length / pitch_factor)

            if new_length > 0:
                # 重采样音频
                indices = np.linspace(0, original_length - 1, new_length)
                if len(audio_data.shape) == 1:  # 单声道
                    processed_audio = np.interp(indices, np.arange(original_length), audio_data)
                else:  # 多声道
                    processed_audio = np.zeros((new_length, audio_data.shape[1]))
                    for channel in range(audio_data.shape[1]):
                        processed_audio[:, channel] = np.interp(
                            indices, np.arange(original_length), audio_data[:, channel]
                        )

                return processed_audio
            else:
                return audio_data

        except Exception as e:
            print(f"❌ 应用音调变化时出错: {e}")
            return audio_data

    def play_audio_with_effects(self, file_path, volume, gain_db, pitch_factor):
        """播放带有音频效果的音频文件"""
        try:
            if not AUDIO_PROCESSING_AVAILABLE:
                # print("⚠️ 音频处理库不可用，跳过效果处理")
                return False

            # 安全地读取音频文件
            audio_data, sample_rate = self.safe_read_audio_file(file_path)
            if audio_data is None or sample_rate is None:
                return False
            # print(f"📁 读取音频文件: {os.path.basename(file_path)}")
            # print(f"  - 采样率: {sample_rate} Hz")
            # print(f"  - 声道数: {audio_data.shape[1] if len(audio_data.shape) > 1 else 1}")
            # print(f"  - 时长: {len(audio_data) / sample_rate:.2f} 秒")

            # 应用增益效果
            if abs(gain_db) > 0.1:  # 只有当增益不为0时才处理
                # print(f"🎚️ 应用增益: {gain_db:.2f} dB")
                audio_data = self.apply_audio_gain(audio_data, gain_db)

            # 应用音调变化
            if abs(pitch_factor - 1.0) > 0.01:  # 只有当音调变化明显时才处理
                # print(f"🎵 应用音调变化: {pitch_factor:.2f}x")
                audio_data = self.apply_pitch_shift(audio_data, sample_rate, pitch_factor)

            # 应用音量
            if abs(volume - 1.0) > 0.01:  # 只有当音量不为1时才处理
                print(f"🔊 应用音量: {volume:.2f}")
                audio_data = audio_data * volume

            # 创建临时文件保存处理后的音频
            import tempfile
            temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            temp_file_path = temp_file.name
            temp_file.close()

            # 保存处理后的音频
            sf.write(temp_file_path, audio_data, sample_rate)

            # 🔧 修复：优先使用指定的音频输出设备播放
            if self.selected_audio_device:
                # 方法1: 直接通过QAudioOutput播放音频流
                success = self.play_audio_through_device(audio_data, sample_rate)
                if success:
                    # print(f"✅ 通过指定设备播放: {self.selected_audio_device.deviceName()}")
                    return True
                else:
                    # print("⚠️ 指定设备播放失败，回退到QMediaPlayer")
                    pass

            # 回退方案：使用QMediaPlayer播放（可能使用默认设备）
            media_content = QMediaContent(QUrl.fromLocalFile(temp_file_path))
            self.media_player.setMedia(media_content)
            self.media_player.setVolume(100)  # 音量已在音频数据中处理
            self.media_player.play()

            # 保存临时文件路径，以便后续清理
            if not hasattr(self, 'temp_audio_files'):
                self.temp_audio_files = []
            self.temp_audio_files.append(temp_file_path)

            # print(f"✅ 音频效果处理完成并开始播放（使用默认设备）")
            return True

        except Exception as e:
            print(f"❌ 音频效果处理失败: {e}")
            return False

    def play_audio_through_device(self, audio_data, sample_rate):
        """通过指定的音频输出设备播放音频数据"""
        try:
            if not self.selected_audio_device:
                print("❌ 没有选择音频输出设备")
                return False

            print(f"🎯 准备通过设备播放: {self.selected_audio_device.deviceName()}")

            # 确保音频数据格式正确
            if len(audio_data.shape) == 1:
                # 单声道转立体声
                audio_data = np.column_stack((audio_data, audio_data))

            # 设置音频格式
            format = QAudioFormat()
            format.setSampleRate(int(sample_rate))
            format.setChannelCount(audio_data.shape[1])  # 根据实际声道数设置
            format.setSampleSize(16)   # 16位
            format.setCodec("audio/pcm")
            format.setByteOrder(QAudioFormat.LittleEndian)
            format.setSampleType(QAudioFormat.SignedInt)

            # 检查设备是否支持此格式
            if not self.selected_audio_device.isFormatSupported(format):
                print("⚠️ 设备不支持指定格式，尝试使用设备首选格式")
                format = self.selected_audio_device.nearestFormat(format)
                print(f"  - 调整后格式: {format.sampleRate()}Hz, {format.channelCount()}声道, {format.sampleSize()}bit")

            # 🔧 修复：停止当前播放并清理资源
            if hasattr(self, 'current_audio_output') and self.current_audio_output:
                try:
                    self.current_audio_output.stop()
                    self.current_audio_output.deleteLater()
                    self.current_audio_output = None
                    print("⏹️ 停止之前的音频播放")
                except Exception as e:
                    print(f"⚠️ 清理音频输出时出错: {e}")
                    self.current_audio_output = None

            # 关闭之前的音频缓冲区
            if hasattr(self, 'audio_buffer') and self.audio_buffer:
                try:
                    self.audio_buffer.close()
                    self.audio_buffer.deleteLater()
                    self.audio_buffer = None
                    print("🗑️ 清理之前的音频缓冲区")
                except Exception as e:
                    print(f"⚠️ 清理音频缓冲区时出错: {e}")
                    self.audio_buffer = None

            # 将音频数据转换为正确的格式
            if format.sampleSize() == 16:
                # 确保数据在正确范围内
                audio_data = np.clip(audio_data, -1.0, 1.0)
                audio_data_int16 = (audio_data * 32767).astype(np.int16)
            else:
                print(f"⚠️ 不支持的采样大小: {format.sampleSize()}")
                return False

            # 创建音频数据的字节流
            audio_bytes = audio_data_int16.tobytes()
            print(f"📊 音频数据: {len(audio_bytes)} 字节, {audio_data.shape[0]} 采样点")

            # 🔑 关键修复：使用QBuffer作为音频数据源
            from PyQt5.QtCore import QBuffer, QByteArray

            # 创建持久的音频缓冲区
            self.audio_buffer = QBuffer()
            self.audio_buffer.setData(QByteArray(audio_bytes))
            self.audio_buffer.open(QIODevice.ReadOnly)

            # 创建新的音频输出对象
            self.current_audio_output = QAudioOutput(self.selected_audio_device, format)

            # 设置音量
            volume_linear = getattr(self, 'volume', 100) / 100.0
            self.current_audio_output.setVolume(volume_linear)

            # 🔑 关键：使用拉取模式播放（类似C++示例）
            self.current_audio_output.start(self.audio_buffer)

            # 检查播放状态
            state = self.current_audio_output.state()
            error = self.current_audio_output.error()

            print(f"🔊 音频播放状态: {state}")
            print(f"🔊 音频错误状态: {error}")

            if state == QAudio.ActiveState:
                print(f"✅ 成功开始通过设备播放: {self.selected_audio_device.deviceName()}")
                print(f"  - 格式: {format.sampleRate()}Hz, {format.channelCount()}声道, {format.sampleSize()}bit")
                print(f"  - 音频数据大小: {len(audio_bytes)} 字节")
                print("🎧 请检查指定设备是否有声音输出！")
                return True
            elif state == QAudio.IdleState:
                print("⚠️ 音频播放处于空闲状态")
                return True  # 空闲状态也可能表示播放成功
            elif state == QAudio.StoppedState:
                print("❌ 音频播放已停止")
                return False
            else:
                print(f"❓ 未知播放状态: {state}")
                # 显示错误信息
                if error != QAudio.NoError:
                    error_messages = {
                        QAudio.OpenError: "打开设备错误",
                        QAudio.IOError: "IO错误",
                        QAudio.UnderrunError: "缓冲区不足错误",
                        QAudio.FatalError: "致命错误"
                    }
                    error_msg = error_messages.get(error, f"未知错误: {error}")
                    print(f"❌ 音频错误详情: {error_msg}")
                return False

        except Exception as e:
            print(f"❌ 通过指定设备播放音频失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def play_basic_audio_file(self, file_path, volume):
        """播放基本音频文件（无特效，但使用指定设备）"""
        try:
            if not AUDIO_PROCESSING_AVAILABLE:
                print("⚠️ 音频处理库不可用，无法进行基本播放")
                return False

            # 安全地读取音频文件
            audio_data, sample_rate = self.safe_read_audio_file(file_path)
            if audio_data is None or sample_rate is None:
                return False

            # 应用音量
            audio_data = audio_data * volume

            # 通过指定设备播放
            if self.selected_audio_device:
                success = self.play_audio_through_device(audio_data, sample_rate)
                if success:
                    print(f"✅ 基本播放成功: {os.path.basename(file_path)}")
                    return True

            print("⚠️ 没有指定的音频输出设备")
            return False

        except Exception as e:
            print(f"❌ 基本音频播放失败: {e}")
            return False

    def cleanup_temp_audio_files(self):
        """清理临时音频文件"""
        if hasattr(self, 'temp_audio_files'):
            for temp_file in self.temp_audio_files:
                try:
                    if os.path.exists(temp_file):
                        os.unlink(temp_file)
                except Exception as e:
                    print(f"清理临时文件失败: {e}")
            self.temp_audio_files.clear()

    def cleanup_audio_resources(self):
        """清理音频资源 - 用于编译版本的安全处理"""
        try:
            # 停止当前音频播放
            if hasattr(self, 'current_audio_output') and self.current_audio_output:
                self.current_audio_output.stop()
                self.current_audio_output.deleteLater()
                self.current_audio_output = None
                print("🗑️ 清理音频输出对象")

            # 关闭音频缓冲区
            if hasattr(self, 'audio_buffer') and self.audio_buffer:
                self.audio_buffer.close()
                self.audio_buffer.deleteLater()
                self.audio_buffer = None
                print("🗑️ 清理音频缓冲区")

            # 停止媒体播放器
            if hasattr(self, 'media_player') and self.media_player:
                self.media_player.stop()
                print("🗑️ 停止媒体播放器")

            # 清理临时文件
            self.cleanup_temp_audio_files()

            # 强制垃圾回收
            import gc
            gc.collect()

        except Exception as e:
            print(f"❌ 清理音频资源时出错: {e}")

    def safe_read_audio_file(self, file_path):
        """安全地读取音频文件"""
        try:
            if not AUDIO_PROCESSING_AVAILABLE:
                return None, None

            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"❌ 音频文件不存在: {file_path}")
                return None, None

            # 检查文件大小（避免读取过大的文件）
            file_size = os.path.getsize(file_path)
            max_size = 100 * 1024 * 1024  # 100MB限制
            if file_size > max_size:
                print(f"❌ 音频文件过大: {file_size} bytes")
                return None, None

            # 尝试读取音频文件
            audio_data, sample_rate = sf.read(file_path)

            # 检查音频数据的有效性
            if audio_data is None or len(audio_data) == 0:
                print(f"❌ 音频文件无效或为空: {file_path}")
                return None, None

            if sample_rate <= 0:
                print(f"❌ 无效的采样率: {sample_rate}")
                return None, None

            return audio_data, sample_rate

        except Exception as e:
            print(f"❌ 读取音频文件失败: {file_path}, 错误: {e}")
            return None, None

    def get_label_style(self):
        """获取标签样式"""
        return """
            QLabel {
                font-size: 11pt;
                font-weight: bold;
                color: #2c3e50;
                padding: 3px;
            }
        """



    def get_combo_style(self):
        """获取下拉框样式"""
        return """
            QComboBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 6px 10px;
                background: white;
                font-size: 10pt;
                min-height: 20px;
            }
            QComboBox:hover {
                border-color: #3498db;
            }
            QComboBox:focus {
                border-color: #2980b9;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #7f8c8d;
                margin-right: 10px;
            }
        """

    def get_spinbox_style(self):
        """获取数字输入框样式"""
        return """
            QSpinBox, QDoubleSpinBox {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                padding: 6px 12px;
                background: white;
                font-size: 10pt;
                min-height: 20px;
                min-width: 80px;
            }
            QSpinBox:hover, QDoubleSpinBox:hover {
                border-color: #3498db;
            }
            QSpinBox:focus, QDoubleSpinBox:focus {
                border-color: #2980b9;
            }
        """

    def get_button_style(self, color):
        """获取按钮样式"""
        return f"""
            QPushButton {{
                background: {color};
                color: white;
                border: none;
                padding: 6px 10px;  # 从8px 12px减小到6px 10px
                border-radius: 6px;
                font-weight: 600;
                font-size: 9pt;     # 从10pt减小到9pt
                text-align: center;
            }}
            QPushButton:hover {{
                background: {color}E6;
                transform: translateY(-1px);
            }}
            QPushButton:pressed {{
                background: {color}CC;
                transform: translateY(0px);
            }}
            QPushButton:disabled {{
                background: #BDBDBD;
                color: #757575;
            }}
        """

    def get_enhanced_button_style(self, color):
        """获取增强版按钮样式"""
        return f"""
            QPushButton {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color}, stop: 1 {color}dd);
                color: white;
                border: 2px solid {color}aa;
                padding: 12px 20px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 12pt;
                text-align: center;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color}ee, stop: 1 {color}cc);
                border-color: {color}cc;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            }}
            QPushButton:pressed {{
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 {color}bb, stop: 1 {color}99);
                border-color: {color}88;
                transform: translateY(0px);
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
            QPushButton:disabled {{
                background: #95a5a6;
                color: #ecf0f1;
                border-color: #7f8c8d;
            }}
        """

    def get_modern_button_style(self, color):
        """获取现代化按钮样式"""
        return f"""
            QPushButton {{
                background: {color};
                color: white;
                border: none;
                padding: 6px 12px;  # 减小内边距从10px 18px到6px 12px
                border-radius: 6px;
                font-weight: 600;
                font-size: 10pt;    # 减小字体从11pt到10pt
                text-align: center;
            }}
            QPushButton:hover {{
                background: {color}E6;
                transform: translateY(-1px);
            }}
            QPushButton:pressed {{
                background: {color}CC;
                transform: translateY(0px);
            }}
            QPushButton:disabled {{
                background: #BDBDBD;
                color: #757575;
            }}
        """



    def _select_audio_folder_safe(self, *args):
        """安全的选择音频文件夹方法"""
        try:
            # 使用上次保存的路径作为起始目录
            start_dir = ""
            if hasattr(self, 'audio_folder_path') and self.audio_folder_path and os.path.exists(self.audio_folder_path):
                start_dir = self.audio_folder_path

            folder_path = QFileDialog.getExistingDirectory(
                self,
                "选择音频文件夹",
                start_dir
            )

            if folder_path:
                self.audio_folder_path = folder_path
                folder_name = os.path.basename(folder_path)

                # 更新界面显示
                if hasattr(self, 'audio_folder_combo'):
                    self.audio_folder_combo.clear()
                    self.audio_folder_combo.addItem(f"📁 {folder_name}")

                # 保存路径到设置
                if hasattr(self, 'save_audio_folder_path'):
                    self.save_audio_folder_path(folder_path)

                if hasattr(self, 'scan_audio_files'):
                    self.scan_audio_files()
                
                if hasattr(self, 'update_audio_status'):
                    self.update_audio_status(f"📁 已选择文件夹: {folder_name}")
                
                print(f"选择音频文件夹: {folder_path}")
        except Exception as e:
            print(f"选择音频文件夹时出错: {e}")
            try:
                QMessageBox.critical(self, "错误", f"选择音频文件夹失败：{str(e)}")
            except:
                print(f"无法显示错误对话框: {e}")

    def scan_audio_files(self):
        """扫描音频文件夹中的音频文件"""
        if not self.audio_folder_path:
            return

        audio_extensions = ['.mp3', '.wav', '.flac', '.aac', '.ogg', '.m4a', '.wma']
        self.audio_files_list = []

        try:
            for root, dirs, files in os.walk(self.audio_folder_path):
                for file in files:
                        file_path = os.path.join(root, file)
                        self.audio_files_list.append(file_path)

            # 默认随机打乱音频文件列表
            if self.audio_files_list:
                import random
                random.shuffle(self.audio_files_list)
                print(f"扫描到 {len(self.audio_files_list)} 个音频文件，已随机排序")
                self.audio_status_label.setText(f"📁 扫描到 {len(self.audio_files_list)} 个音频文件（随机排序）")
            else:
                print("未找到音频文件")
                self.audio_status_label.setText("❌ 未找到音频文件")

        except Exception as e:
            print(f"扫描音频文件时出错: {e}")
            self.audio_status_label.setText("❌ 扫描音频文件失败")

    def start_audio_playback(self, *args):
        """开始音频播放"""
        try:
            if not self.audio_files_list:
                QMessageBox.warning(self, "提示", "请先选择包含音频文件的文件夹！")
                return

            # 统一使用完整音频处理功能
            print("🔧 使用完整音频处理功能")
            # 先清理之前的音频资源
            self.cleanup_audio_resources()

            self.is_audio_playing = True
            self.start_audio_btn.setEnabled(False)
            self.stop_audio_btn.setEnabled(True)

            # 开始播放第一个音频
            self.current_audio_index = 0
            self.play_current_audio()

            print("开始音频播放")
            self.audio_status_label.setText("🎵 正在播放音频...")

        except Exception as e:
            print(f"❌ 启动音频播放时出错: {e}")
            self.is_audio_playing = False
            self.start_audio_btn.setEnabled(True)
            self.stop_audio_btn.setEnabled(False)
            QMessageBox.critical(self, "错误", f"启动音频播放失败：{str(e)}")

    def stop_audio_playback(self, *args):
        """停止音频播放"""
        self.is_audio_playing = False
        self.audio_timer.stop()

        # 停止QMediaPlayer
        self.media_player.stop()

        # 🔧 修复：同时停止QAudioOutput播放
        if hasattr(self, 'current_audio_output') and self.current_audio_output:
            self.current_audio_output.stop()
            self.current_audio_output = None
            print("⏹️ QAudioOutput播放已停止")

        # 关闭音频缓冲区
        if hasattr(self, 'audio_buffer') and self.audio_buffer:
            self.audio_buffer.close()
            self.audio_buffer = None
            print("🗑️ 音频缓冲区已清理")

        # 清理临时音频文件
        self.cleanup_temp_audio_files()

        self.start_audio_btn.setEnabled(True)
        self.stop_audio_btn.setEnabled(False)

        print("停止音频播放")
        self.audio_status_label.setText("⏹️ 音频播放已停止")

    def play_current_audio(self):
        """播放当前音频"""
        try:
            if not self.is_audio_playing or not self.audio_files_list:
                return

            if self.current_audio_index >= len(self.audio_files_list):
                self.current_audio_index = 0

            current_file = self.audio_files_list[self.current_audio_index]
            file_name = os.path.basename(current_file)

            # 检查文件是否存在
            if not os.path.exists(current_file):
                print(f"❌ 音频文件不存在: {current_file}")
                self.current_audio_index += 1
                self.play_current_audio()  # 尝试下一个文件
                return

            # 生成随机音频参数
            volume = random.uniform(self.volume1_min_spin.value(), self.volume1_max_spin.value())
            gain = random.uniform(self.gain_min_spin.value(), self.gain_max_spin.value())
            pitch = random.uniform(self.pitch_min_spin.value(), self.pitch_max_spin.value())

            # 设置音频输出设备
            device_set_success = False
            selected_device_name = self.output_device_combo.currentText()
            selected_device_data = self.output_device_combo.currentData()

            if selected_device_data:
                try:
                    # 如果是Qt设备对象，设置为音频输出设备
                    if hasattr(selected_device_data, 'deviceName') and not selected_device_data.isNull():
                        device_set_success = self.set_audio_output_device(selected_device_data)
                        if device_set_success:
                            print(f"✅ 音频将输出到: {selected_device_data.deviceName()}")
                        else:
                            print(f"⚠️ 无法设置设备 {selected_device_data.deviceName()}，使用默认设备")
                    elif isinstance(selected_device_data, str):
                        print(f"ℹ️ Windows设备字符串: {selected_device_data}，使用系统默认输出")
                    else:
                        print(f"ℹ️ 备用设备选项: {selected_device_name}，使用系统默认输出")
                except Exception as e:
                    print(f"❌ 设置音频设备时出错: {e}")

            if not device_set_success:
                print(f"ℹ️ 使用系统默认音频设备播放")

            # 应用音频效果并播放
            success = False

            # 统一使用完整的音频效果处理
            try:
                success = self.play_audio_with_effects(current_file, volume, gain, pitch)
            except Exception as e:
                print(f"❌ 音频效果处理失败: {e}")
                success = False

            if not success:
                # 如果音频处理失败，尝试基本播放
                print("⚠️ 音频效果处理失败，尝试基本播放")
                try:
                    success = self.play_basic_audio_file(current_file, volume)
                except Exception as e:
                    print(f"❌ 基本播放失败: {e}")
                    success = False

                if not success:
                    # 最后的回退：使用QMediaPlayer
                    print("⚠️ 基本播放也失败，使用QMediaPlayer")
                    try:
                        media_content = QMediaContent(QUrl.fromLocalFile(current_file))
                        self.media_player.setMedia(media_content)
                        self.media_player.setVolume(int(volume * 100))
                        self.media_player.play()
                        success = True
                    except Exception as e:
                        print(f"❌ QMediaPlayer播放失败: {e}")
                        success = False

            # 播放模式处理
            play_mode = self.play_mode_combo.currentText()
            if play_mode == "片段播放":
                # 片段播放：播放指定时长后停止当前音频
                segment_duration = self.segment_duration_spin.value() * 1000  # 转换为毫秒
                QTimer.singleShot(segment_duration, self.stop_current_segment)
                print(f"片段播放音频: {file_name} (时长: {self.segment_duration_spin.value()}秒)")
                print(f"  - 音量: {volume:.2f}, 增益: {gain:.2f}dB, 音调: {pitch:.2f}x")
                self.audio_status_label.setText(f"🎵 片段播放: {file_name} ({self.segment_duration_spin.value()}秒)")
            else:
                # 完整播放
                print(f"完整播放音频: {file_name}")
                print(f"  - 音量: {volume:.2f}, 增益: {gain:.2f}dB, 音调: {pitch:.2f}x")
                self.audio_status_label.setText(f"🎵 完整播放: {file_name}")

            # 设置下次播放的间隔时间
            interval = random.uniform(self.interval_min_spin.value(), self.interval_max_spin.value())
            self.audio_timer.start(int(interval * 1000))  # 转换为毫秒

        except Exception as e:
            print(f"❌ 播放音频时发生严重错误: {e}")
            import traceback
            traceback.print_exc()

            # 尝试继续播放下一个音频
            try:
                self.current_audio_index += 1
                if self.current_audio_index < len(self.audio_files_list):
                    # 延迟一段时间后尝试下一个音频
                    QTimer.singleShot(2000, self.play_current_audio)
                else:
                    # 如果到了列表末尾，停止播放
                    print("❌ 音频列表播放完毕，停止播放")
                    self.stop_audio_playback()
            except Exception as e2:
                print(f"❌ 尝试恢复播放时也出错: {e2}")
                self.stop_audio_playback()

    def stop_current_segment(self):
        """停止当前片段播放"""
        # 停止QMediaPlayer
        self.media_player.stop()

        # 🔧 修复：同时停止QAudioOutput播放
        if hasattr(self, 'current_audio_output') and self.current_audio_output:
            self.current_audio_output.stop()
            print("⏹️ 片段播放已停止（QAudioOutput）")

        # 关闭音频缓冲区
        if hasattr(self, 'audio_buffer') and self.audio_buffer:
            self.audio_buffer.close()
            self.audio_buffer = None

        print("⏹️ 片段播放已停止")

    def play_next_audio_segment(self, *args):
        """播放下一个音频（默认随机选择）"""
        if not self.is_audio_playing:
            return

        # 随机选择下一个音频文件（默认行为）
        if len(self.audio_files_list) > 1:
            import random
            # 确保不会连续播放同一个文件
            new_index = self.current_audio_index
            while new_index == self.current_audio_index:
                new_index = random.randint(0, len(self.audio_files_list) - 1)
            self.current_audio_index = new_index
        else:
            # 如果只有一个文件，就重复播放
            self.current_audio_index = 0

        # 播放下一个音频
        self.play_current_audio()

    # --- 新增：一键启动相关方法 --- #
    def show_quick_start_dialog(self, *args):
        """显示一键启动功能选择对话框"""
        try:
            if not self.is_connected:
                QMessageBox.warning(self, "提示", "请先连接到OBS！")
                return

            if self.is_batch_running:
                # 如果正在运行，则停止所有功能
                self.stop_all_functions()
                return

            # 显示功能选择对话框
            dialog = QuickStartDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                selected_functions = dialog.selected_functions
                if selected_functions:
                    self.start_selected_functions(selected_functions)
        except Exception as e:
            print(f"显示一键启动对话框时出错: {e}")
            import traceback
            traceback.print_exc()

    def start_selected_functions(self, functions):
        """启动选中的功能"""
        try:
            print(f"开始启动选中的功能: {functions}")

            success_count = 0
            failed_functions = []

            for func_id in functions:
                try:
                    # 添加延迟以避免过快的连续操作
                    QApplication.processEvents()
                    success = self.start_single_function(func_id)
                    if success:
                        success_count += 1
                        self.active_functions.append(func_id)
                        print(f"✅ 成功启动功能: {func_id}")
                    else:
                        failed_functions.append(func_id)
                        print(f"❌ 启动功能失败: {func_id}")
                except Exception as e:
                    print(f"启动功能 {func_id} 时出错: {e}")
                    import traceback
                    traceback.print_exc()
                    failed_functions.append(func_id)

            if success_count > 0:
                self.is_batch_running = True
                self.quick_start_button.setText('停止去重')
                self.quick_start_button.setStyleSheet("""
                    QPushButton {
                        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
                        color: white !important;
                        border: 2px solid #dc2626 !important;
                        padding: 8px 16px;
                        border-radius: 6px;
                        font-weight: bold;
                        min-width: 100px;
                        font-size: 11pt;
                    }
                    QPushButton:enabled {
                        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
                        border: 2px solid #dc2626 !important;
                    }
                    QPushButton:hover:enabled {
                        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
                        border: 2px solid #b91c1c !important;
                        transform: translateY(-1px);
                    }
                    QPushButton:pressed:enabled {
                        background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%) !important;
                        border: 2px solid #991b1b !important;
                        transform: translateY(0px);
                    }
                """)

                # 显示启动结果
                if failed_functions:
                    QMessageBox.warning(self, "部分功能启动失败",
                        f"成功启动 {success_count} 个功能\n失败功能: {', '.join(failed_functions)}")
                else:
                    QMessageBox.information(self, "启动成功", f"成功启动 {success_count} 个去重功能！")
            else:
                QMessageBox.critical(self, "启动失败", "所有功能启动失败，请检查OBS连接和媒体源设置！")
        except Exception as e:
            print(f"start_selected_functions 出错: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.critical(self, "错误", f"启动功能时发生错误: {str(e)}")

    def start_single_function(self, func_id):
        """启动单个功能"""
        try:
            print(f"正在启动功能: {func_id}")

            # 添加短暂延迟以避免过快操作
            import time
            time.sleep(0.1)

            if func_id == "speed":
                if not self.video_source_combo.currentData():
                    print("错误：未选择视频媒体源")
                    return False
                # 使用QTimer延迟设置，避免直接调用可能导致的递归
                QTimer.singleShot(50, lambda: self.speed_checkbox.setChecked(True))
                QApplication.processEvents()
                return True

            elif func_id == "blur":
                if not self.video_source_combo.currentData():
                    print("错误：未选择视频媒体源")
                    return False
                QTimer.singleShot(50, lambda: self.blur_checkbox.setChecked(True))
                QApplication.processEvents()
                return True

            elif func_id == "transform":
                if not self.video_source_combo.currentData():
                    print("错误：未选择视频媒体源")
                    return False
                QTimer.singleShot(50, lambda: self.transform_checkbox.setChecked(True))
                QApplication.processEvents()
                return True

            elif func_id == "color":
                if not self.video_source_combo.currentData():
                    print("错误：未选择视频媒体源")
                    return False
                QTimer.singleShot(50, lambda: self.color_checkbox.setChecked(True))
                QApplication.processEvents()
                return True

            elif func_id == "audio_eq":
                if not self.audio_source_combo.currentData():
                    print("错误：未选择音频媒体源")
                    return False
                QTimer.singleShot(50, lambda: self.audio_checkbox.setChecked(True))
                QApplication.processEvents()
                return True

            elif func_id == "compressor":
                if not self.audio_source_combo.currentData():
                    print("错误：未选择音频媒体源")
                    return False
                QTimer.singleShot(50, lambda: self.compressor_enable_checkbox.setChecked(True))
                QApplication.processEvents()
                return True

            elif func_id == "gain":
                if not self.audio_source_combo.currentData():
                    print("错误：未选择音频媒体源")
                    return False
                QTimer.singleShot(50, lambda: self.gain_enable_checkbox.setChecked(True))
                QApplication.processEvents()
                return True

            elif func_id == "audio_mute":
                if not self.audio_source_combo.currentData():
                    print("错误：未选择音频媒体源")
                    return False
                QTimer.singleShot(50, lambda: self.audio_mute_checkbox.setChecked(True))
                QApplication.processEvents()
                return True

            elif func_id == "audio_volume":
                if not self.audio_source_combo.currentData():
                    print("错误：未选择音频媒体源")
                    return False
                QTimer.singleShot(50, lambda: self.audio_volume_checkbox.setChecked(True))
                QApplication.processEvents()
                return True

            elif func_id == "plugin_dedup":
                if not self.audio_source_combo.currentData():
                    print("错误：未选择音频媒体源")
                    return False
                QTimer.singleShot(50, lambda: self.plugin_dedup_checkbox.setChecked(True))
                QApplication.processEvents()
                return True

            print(f"未知的功能ID: {func_id}")
            return False
        except Exception as e:
            print(f"启动功能 {func_id} 时出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    def stop_all_functions(self):
        """停止所有正在运行的功能"""
        print("停止所有去重功能...")

        # 停止所有活跃的功能
        for func_id in self.active_functions:
            try:
                self.stop_single_function(func_id)
            except Exception as e:
                print(f"停止功能 {func_id} 时出错: {e}")

        # 重置状态
        self.is_batch_running = False
        self.active_functions = []
        self.quick_start_button.setText('一键启动')
        self.quick_start_button.setStyleSheet("""
            QPushButton {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
                color: white !important;
                border: 2px solid #059669 !important;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
                font-size: 11pt;
            }
            QPushButton:enabled {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
                color: white !important;
                border: 2px solid #059669 !important;
            }
            QPushButton:hover:enabled {
                background: linear-gradient(135deg, #059669 0%, #047857 100%) !important;
                border: 2px solid #047857 !important;
                transform: translateY(-1px);
            }
            QPushButton:pressed:enabled {
                background: linear-gradient(135deg, #047857 0%, #065f46 100%) !important;
                border: 2px solid #065f46 !important;
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background: #9ca3af !important;
                color: #6b7280 !important;
                border: 2px solid #9ca3af !important;
            }
        """)

        QMessageBox.information(self, "停止成功", "所有去重功能已停止！")

    def stop_single_function(self, func_id):
        """停止单个功能"""
        if func_id == "speed":
            self.speed_checkbox.setChecked(False)
        elif func_id == "blur":
            self.blur_checkbox.setChecked(False)
        elif func_id == "transform":
            self.transform_checkbox.setChecked(False)
        elif func_id == "color":
            self.color_checkbox.setChecked(False)
        elif func_id == "audio_eq":
            self.audio_checkbox.setChecked(False)
        elif func_id == "compressor":
            self.compressor_enable_checkbox.setChecked(False)
        elif func_id == "gain":
            self.gain_enable_checkbox.setChecked(False)
        elif func_id == "audio_mute":
            self.audio_mute_checkbox.setChecked(False)
        elif func_id == "audio_volume":
            self.audio_volume_checkbox.setChecked(False)
        elif func_id == "plugin_dedup":
            self.plugin_dedup_checkbox.setChecked(False)

    # --- 新增：音频播放器辅助方法 --- #
    def get_control_button_style(self):
        """获取播放控制按钮样式"""
        return """
            QPushButton {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border: none;
                padding: 12px 16px;
                border-radius: 8px;
                font-size: 16pt;
                font-weight: bold;
                min-width: 50px;
                min-height: 50px;
            }
            QPushButton:hover {
                background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
                transform: translateY(0px);
            }
        """

    def get_mode_button_style(self):
        """获取模式按钮样式"""
        return """
            QPushButton {
                background: #f1f5f9;
                color: #64748b;
                border: 2px solid #e2e8f0;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #e2e8f0;
                border-color: #cbd5e1;
            }
            QPushButton:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                border-color: #059669;
            }
        """



    # --- 新增：加载设置 --- #
    def load_settings(self):
        """从 QSettings 加载所有模块的 UI 设置。"""
        print("🔄 正在加载设置...")
        print(f"🔧 设置文件路径: {self.settings.fileName()}")
        # 速度控制
        print("📊 加载速度控制设置...")
        self.settings.beginGroup("SpeedControl")
        enabled = self.settings.value("enabled", False, type=bool)
        min_speed = self.settings.value("min_speed", self.speed_control['min_speed'], type=int)
        max_speed = self.settings.value("max_speed", self.speed_control['max_speed'], type=int)

        self.speed_checkbox.setChecked(enabled)
        self.speed_min_spinbox.setValue(min_speed)
        self.speed_max_spinbox.setValue(max_speed)

        # 同时更新内存变量
        self.speed_control['enabled'] = enabled
        self.speed_control['min_speed'] = min_speed
        self.speed_control['max_speed'] = max_speed
        self.settings.endGroup()

        # 模糊控制
        print("📊 加载模糊控制设置...")
        self.settings.beginGroup("BlurControl")
        enabled = self.settings.value("enabled", False, type=bool)
        filter_name = self.settings.value("filter_name", self.blur_control['filter_name'], type=str)
        min_radius = self.settings.value("min_radius", self.blur_control['min_radius'], type=float)
        max_radius = self.settings.value("max_radius", self.blur_control['max_radius'], type=float)
        interval_ms = self.settings.value("interval_ms", self.blur_control['interval_ms'], type=int)

        self.blur_checkbox.setChecked(enabled)
        self.blur_filter_name_edit.setText(filter_name)
        self.blur_min_radius_spin.setValue(min_radius)
        self.blur_max_radius_spin.setValue(max_radius)
        self.blur_interval_spin.setValue(interval_ms)

        # 同时更新内存变量
        self.blur_control['enabled'] = enabled
        self.blur_control['filter_name'] = filter_name
        self.blur_control['min_radius'] = min_radius
        self.blur_control['max_radius'] = max_radius
        self.blur_control['interval_ms'] = interval_ms
        self.settings.endGroup()

        # 移动控制
        self.settings.beginGroup("TransformControl")
        self.transform_checkbox.setChecked(self.settings.value("enabled", False, type=bool))
        self.transform_scale_spin.setValue(self.settings.value("fixed_scale", self.transform_control['fixed_scale'], type=int))
        self.transform_interval_spin.setValue(self.settings.value("interval_secs", self.transform_control['interval_secs'], type=float))
        self.transform_transition_spin.setValue(self.settings.value("transition_secs", self.transform_control['transition_secs'], type=float))
        self.settings.endGroup()

        # 颜色控制
        self.settings.beginGroup("ColorControl")
        self.color_checkbox.setChecked(self.settings.value("enabled", False, type=bool))
        self.color_filter_name_edit.setText(self.settings.value("filter_name", self.color_control['filter_name'], type=str))
        for param in ["hue", "saturation", "brightness", "contrast", "gamma"]:
            min_spin = getattr(self, f"color_{param}_min_spin")
            max_spin = getattr(self, f"color_{param}_max_spin")
            min_spin.setValue(self.settings.value(f"min_{param}", self.color_control[f'min_{param}'], type=float))
            max_spin.setValue(self.settings.value(f"max_{param}", self.color_control[f'max_{param}'], type=float))
        self.color_interval_spin.setValue(self.settings.value("interval_ms", self.color_control['interval_ms'], type=int))
        self.settings.endGroup()

        # 音频EQ控制
        print("📊 加载音频EQ控制设置...")
        self.settings.beginGroup("AudioEqControl")
        enabled = self.settings.value("enabled", False, type=bool)
        filter_name = self.settings.value("filter_name", self.audio_eq_control['filter_name'], type=str)
        interval_secs = self.settings.value("interval_secs", self.audio_eq_control['interval_secs'], type=float)

        self.audio_checkbox.setChecked(enabled)
        self.audio_filter_name_edit.setText(filter_name)
        self.audio_interval_spin.setValue(interval_secs)

        # 同时更新内存变量
        self.audio_eq_control['enabled'] = enabled
        self.audio_eq_control['filter_name'] = filter_name
        self.audio_eq_control['interval_secs'] = interval_secs

        for band in ["low", "mid", "high"]:
            min_gain = self.settings.value(f"min_{band}_gain", self.audio_eq_control[f'min_{band}_gain'], type=float)
            max_gain = self.settings.value(f"max_{band}_gain", self.audio_eq_control[f'max_{band}_gain'], type=float)

            min_spin = getattr(self, f"audio_{band}_min_spin")
            max_spin = getattr(self, f"audio_{band}_max_spin")
            min_spin.setValue(min_gain)
            max_spin.setValue(max_gain)

            # 同时更新内存变量
            self.audio_eq_control[f'min_{band}_gain'] = min_gain
            self.audio_eq_control[f'max_{band}_gain'] = max_gain
        self.settings.endGroup()

        # 自动压缩控制
        print("📊 加载自动压缩控制设置...")
        self.settings.beginGroup("CompressorControl")
        enabled = self.settings.value("enabled", False, type=bool)
        filter_name = self.settings.value("filter_name", self.compressor_control['filter_name'], type=str)
        interval_secs = self.settings.value("interval_secs", self.compressor_control['interval_secs'], type=float)

        self.compressor_enable_checkbox.setChecked(enabled)
        self.compressor_filter_name_edit.setText(filter_name)
        self.compressor_interval_spin.setValue(interval_secs)

        # 同时更新内存变量
        self.compressor_control['enabled'] = enabled
        self.compressor_control['filter_name'] = filter_name
        self.compressor_control['interval_secs'] = interval_secs

        for param in ["ratio", "threshold", "output_gain", "release"]:
            min_value = self.settings.value(f"min_{param}", self.compressor_control[f'min_{param}'], type=float)
            max_value = self.settings.value(f"max_{param}", self.compressor_control[f'max_{param}'], type=float)

            min_spin = getattr(self, f"compressor_{param}_min_spin")
            max_spin = getattr(self, f"compressor_{param}_max_spin")
            min_spin.setValue(min_value)
            max_spin.setValue(max_value)

            # 同时更新内存变量
            self.compressor_control[f'min_{param}'] = min_value
            self.compressor_control[f'max_{param}'] = max_value
        self.settings.endGroup()

        # 自动增益控制
        print("📊 加载自动增益控制设置...")
        self.settings.beginGroup("GainControl")
        enabled = self.settings.value("enabled", False, type=bool)
        filter_name = self.settings.value("filter_name", self.gain_control['filter_name'], type=str)
        min_gain = self.settings.value("min_gain", self.gain_control['min_gain'], type=float)
        max_gain = self.settings.value("max_gain", self.gain_control['max_gain'], type=float)
        interval_secs = self.settings.value("interval_secs", self.gain_control['interval_secs'], type=float)

        self.gain_enable_checkbox.setChecked(enabled)
        self.gain_filter_name_edit.setText(filter_name)
        self.gain_interval_spin.setValue(interval_secs)

        min_spin = getattr(self, "gain_gain_min_spin")
        max_spin = getattr(self, "gain_gain_max_spin")
        min_spin.setValue(min_gain)
        max_spin.setValue(max_gain)

        # 同时更新内存变量
        self.gain_control['enabled'] = enabled
        self.gain_control['filter_name'] = filter_name
        self.gain_control['min_gain'] = min_gain
        self.gain_control['max_gain'] = max_gain
        self.gain_control['interval_secs'] = interval_secs
        self.settings.endGroup()

        # 断音控制
        print("📊 加载断音控制设置...")
        self.settings.beginGroup("AudioMuteControl")
        enabled = self.settings.value("enabled", False, type=bool)
        min_interval_secs = self.settings.value("min_interval_secs", self.audio_mute_control['min_interval_secs'], type=float)
        max_interval_secs = self.settings.value("max_interval_secs", self.audio_mute_control['max_interval_secs'], type=float)
        min_mute_duration_secs = self.settings.value("min_mute_duration_secs", self.audio_mute_control['min_mute_duration_secs'], type=float)
        max_mute_duration_secs = self.settings.value("max_mute_duration_secs", self.audio_mute_control['max_mute_duration_secs'], type=float)

        self.audio_mute_checkbox.setChecked(enabled)
        self.audio_mute_interval_min_spin.setValue(min_interval_secs)
        self.audio_mute_interval_max_spin.setValue(max_interval_secs)
        self.audio_mute_duration_min_spin.setValue(min_mute_duration_secs)
        self.audio_mute_duration_max_spin.setValue(max_mute_duration_secs)

        # 同时更新内存变量
        self.audio_mute_control['enabled'] = enabled
        self.audio_mute_control['min_interval_secs'] = min_interval_secs
        self.audio_mute_control['max_interval_secs'] = max_interval_secs
        self.audio_mute_control['min_mute_duration_secs'] = min_mute_duration_secs
        self.audio_mute_control['max_mute_duration_secs'] = max_mute_duration_secs
        self.settings.endGroup()

        # 随机音频播放大小控制
        print("📊 加载音频音量控制设置...")
        self.settings.beginGroup("AudioVolumeControl")
        enabled = self.settings.value("enabled", False, type=bool)
        volume_min_percent = self.settings.value("volume_min_percent", self.audio_volume_control['volume_min_percent'], type=int)
        volume_max_percent = self.settings.value("volume_max_percent", self.audio_volume_control['volume_max_percent'], type=int)
        min_interval_secs = self.settings.value("min_interval_secs", self.audio_volume_control['min_interval_secs'], type=float)
        max_interval_secs = self.settings.value("max_interval_secs", self.audio_volume_control['max_interval_secs'], type=float)

        self.audio_volume_checkbox.setChecked(enabled)
        self.audio_volume_min_spin.setValue(volume_min_percent)
        self.audio_volume_max_spin.setValue(volume_max_percent)
        self.audio_volume_interval_min_spin.setValue(min_interval_secs)
        self.audio_volume_interval_max_spin.setValue(max_interval_secs)

        # 同时更新内存变量
        self.audio_volume_control['enabled'] = enabled
        self.audio_volume_control['volume_min_percent'] = volume_min_percent
        self.audio_volume_control['volume_max_percent'] = volume_max_percent
        self.audio_volume_control['min_interval_secs'] = min_interval_secs
        self.audio_volume_control['max_interval_secs'] = max_interval_secs
        self.settings.endGroup()

        # 插件去重控制
        self.settings.beginGroup("PluginDedupControl")
        self.plugin_dedup_checkbox.setChecked(self.settings.value("enabled", False, type=bool))

        # 加载每个插件的设置
        for i, plugin in enumerate(self.plugin_dedup_control["plugins"]):
            plugin_name = plugin["name"]
            self.settings.beginGroup(f"Plugin_{i}_{plugin_name}")

            # 更新插件配置
            plugin["min_interval_secs"] = self.settings.value("min_interval_secs", plugin["min_interval_secs"], type=float)
            plugin["max_interval_secs"] = self.settings.value("max_interval_secs", plugin["max_interval_secs"], type=float)
            plugin["min_duration_secs"] = self.settings.value("min_duration_secs", plugin["min_duration_secs"], type=float)
            plugin["max_duration_secs"] = self.settings.value("max_duration_secs", plugin["max_duration_secs"], type=float)

            # 更新UI控件
            if i < len(self.plugin_controls):
                controls = self.plugin_controls[i]
                controls['interval_min'].setValue(plugin["min_interval_secs"])
                controls['interval_max'].setValue(plugin["max_interval_secs"])
                controls['duration_min'].setValue(plugin["min_duration_secs"])
                controls['duration_max'].setValue(plugin["max_duration_secs"])

            self.settings.endGroup()

        self.settings.endGroup()

        print("✅ 设置加载完成！")
    # ------------------ #

    # --- 新增：保存设置 --- #
    def save_settings(self):
        """将所有模块的 UI 设置保存到 QSettings。"""
        print("正在保存设置...")
        # 速度控制 - 从内存变量读取，确保数据一致性
        self.settings.beginGroup("SpeedControl")
        self.settings.setValue("enabled", self.speed_control.get("enabled", False))
        self.settings.setValue("min_speed", self.speed_control.get("min_speed", 90))
        self.settings.setValue("max_speed", self.speed_control.get("max_speed", 120))
        self.settings.endGroup()

        # 模糊控制 - 从内存变量读取，确保数据一致性
        self.settings.beginGroup("BlurControl")
        self.settings.setValue("enabled", self.blur_control.get("enabled", False))
        self.settings.setValue("filter_name", self.blur_control.get("filter_name", "Composite Blur"))
        self.settings.setValue("min_radius", self.blur_control.get("min_radius", 0.0))
        self.settings.setValue("max_radius", self.blur_control.get("max_radius", 2.0))
        self.settings.setValue("interval_ms", self.blur_control.get("interval_ms", 1000))
        self.settings.endGroup()

        # 移动控制 - 从内存变量读取，确保数据一致性
        self.settings.beginGroup("TransformControl")
        self.settings.setValue("enabled", self.transform_control.get("enabled", False))
        self.settings.setValue("fixed_scale", self.transform_control.get("fixed_scale", 110))
        self.settings.setValue("interval_secs", self.transform_control.get("interval_secs", 2.0))
        self.settings.setValue("transition_secs", self.transform_control.get("transition_secs", 10.0))
        self.settings.endGroup()

        # 颜色控制 - 从内存变量读取，确保数据一致性
        self.settings.beginGroup("ColorControl")
        self.settings.setValue("enabled", self.color_control.get("enabled", False))
        self.settings.setValue("filter_name", self.color_control.get("filter_name", "自动颜色校正"))
        for param in ["hue", "saturation", "brightness", "contrast", "gamma"]:
            min_key = f"min_{param}"
            max_key = f"max_{param}"
            self.settings.setValue(min_key, self.color_control.get(min_key, 0.0))
            self.settings.setValue(max_key, self.color_control.get(max_key, 0.0))
        self.settings.setValue("interval_ms", self.color_control.get("interval_ms", 1500))
        self.settings.endGroup()

        # 音频EQ控制 - 从内存变量读取，确保数据一致性
        self.settings.beginGroup("AudioEqControl")
        self.settings.setValue("enabled", self.audio_eq_control.get("enabled", False))
        self.settings.setValue("filter_name", self.audio_eq_control.get("filter_name", "3段式均衡器"))
        for band in ["low", "mid", "high"]:
            min_key = f"min_{band}_gain"
            max_key = f"max_{band}_gain"
            self.settings.setValue(min_key, self.audio_eq_control.get(min_key, -2.0))
            self.settings.setValue(max_key, self.audio_eq_control.get(max_key, 2.0))
        self.settings.setValue("interval_secs", self.audio_eq_control.get("interval_secs", 1.0))
        self.settings.endGroup()

        # 自动压缩控制 - 从内存变量读取，确保数据一致性
        self.settings.beginGroup("CompressorControl")
        self.settings.setValue("enabled", self.compressor_control.get("enabled", False))
        self.settings.setValue("filter_name", self.compressor_control.get("filter_name", "自动压缩控制"))
        for param in ["ratio", "threshold", "output_gain", "release"]:
            min_key = f"min_{param}"
            max_key = f"max_{param}"
            self.settings.setValue(min_key, self.compressor_control.get(min_key, 0.0))
            self.settings.setValue(max_key, self.compressor_control.get(max_key, 0.0))
        self.settings.setValue("interval_secs", self.compressor_control.get("interval_secs", 1.0))
        self.settings.endGroup()

        # 自动增益控制 - 从内存变量读取，确保数据一致性
        self.settings.beginGroup("GainControl")
        self.settings.setValue("enabled", self.gain_control.get("enabled", False))
        self.settings.setValue("filter_name", self.gain_control.get("filter_name", "自动增益控制"))
        self.settings.setValue("min_gain", self.gain_control.get("min_gain", -3.0))
        self.settings.setValue("max_gain", self.gain_control.get("max_gain", 3.0))
        self.settings.setValue("interval_secs", self.gain_control.get("interval_secs", 1.0))
        self.settings.endGroup()

        # 断音控制 - 从内存变量读取，确保数据一致性
        self.settings.beginGroup("AudioMuteControl")
        self.settings.setValue("enabled", self.audio_mute_control.get("enabled", False))
        self.settings.setValue("min_interval_secs", self.audio_mute_control.get("min_interval_secs", 30.0))
        self.settings.setValue("max_interval_secs", self.audio_mute_control.get("max_interval_secs", 40.0))
        self.settings.setValue("min_mute_duration_secs", self.audio_mute_control.get("min_mute_duration_secs", 0.1))
        self.settings.setValue("max_mute_duration_secs", self.audio_mute_control.get("max_mute_duration_secs", 0.5))
        self.settings.endGroup()

        # 随机音频播放大小控制 - 从内存变量读取，确保数据一致性
        self.settings.beginGroup("AudioVolumeControl")
        self.settings.setValue("enabled", self.audio_volume_control.get("enabled", False))
        self.settings.setValue("volume_min_percent", self.audio_volume_control.get("volume_min_percent", 60))
        self.settings.setValue("volume_max_percent", self.audio_volume_control.get("volume_max_percent", 80))
        self.settings.setValue("min_interval_secs", self.audio_volume_control.get("min_interval_secs", 1.0))
        self.settings.setValue("max_interval_secs", self.audio_volume_control.get("max_interval_secs", 5.0))
        self.settings.endGroup()

        # 插件去重控制
        self.settings.beginGroup("PluginDedupControl")
        self.settings.setValue("enabled", self.plugin_dedup_checkbox.isChecked())

        # 保存每个插件的设置
        for i, plugin in enumerate(self.plugin_dedup_control["plugins"]):
            plugin_name = plugin["name"]
            self.settings.beginGroup(f"Plugin_{i}_{plugin_name}")

            self.settings.setValue("min_interval_secs", plugin["min_interval_secs"])
            self.settings.setValue("max_interval_secs", plugin["max_interval_secs"])
            self.settings.setValue("min_duration_secs", plugin["min_duration_secs"])
            self.settings.setValue("max_duration_secs", plugin["max_duration_secs"])

            self.settings.endGroup()

        self.settings.endGroup()

        # 确保设置被写入磁盘
        self.settings.sync()
        # print("设置保存完成。")
    # ------------------ #

    # --- 新增：重写 closeEvent --- #
    def closeEvent(self, event):
        """窗口关闭事件，在关闭前保存设置。"""
        self.save_settings() # 调用保存方法

        # 🔧 新增：保存音频播放器参数
        self.save_audio_player_settings()

        super().closeEvent(event) # 调用父类的 closeEvent
    # ------------------------- #

    def setup_timers(self):
        """设置所有模块的定时器回调函数。"""
        self.speed_control["timer"].timeout.connect(self.monitor_playback_state)
        self.blur_control["timer"].timeout.connect(self.execute_blur_change)
        # --- 恢复为检查动画状态的回调 --- #
        self.transform_control["timer"].timeout.connect(self.check_transform_animation_status)
        # ------------------------------ #
        # 连接颜色定时器
        self.color_control["timer"].timeout.connect(self.execute_color_change)
        # --- 连接其他定时器 (待实现回调函数) ---
        # self.audio_eq_control["timer"].timeout.connect(self.execute_audio_eq_change)
        # 连接音频定时器 (注意间隔单位是秒)
        self.audio_eq_control["timer"].timeout.connect(self.execute_audio_eq_change)

        # 连接插件去重定时器 - 每个插件独立的定时器
        for i, plugin in enumerate(self.plugin_dedup_control["plugins"]):
            plugin["timer"].timeout.connect(lambda checked=False, idx=i: self.activate_plugin_by_index(idx))
            plugin["duration_timer"].timeout.connect(lambda checked=False, idx=i: self.deactivate_plugin_by_index(idx))

    def connect_signals(self):
        """连接信号 - 大部分信号已在UI创建时连接"""
        # print("连接信号完成 - 使用新的UI结构")



    def connect_to_obs(self, checked=False):
        """
        连接或断开与 OBS WebSocket 的连接，并处理识别握手。
        """
        if not self.is_connected:
            obs_ws_url = "ws://localhost:4455"
            self.status_label.setText(f"尝试连接到 {obs_ws_url}...")
            QApplication.processEvents()

            temp_ws = None # 使用临时变量，握手成功再赋值给 self.ws
            try:
                # 1. 创建 WebSocket 连接
                temp_ws = websocket.create_connection(obs_ws_url, timeout=5)
                # print("WebSocket 连接已建立，等待 Hello...")

                # 2. 接收 Hello 消息 (Opcode 0)
                hello_raw = temp_ws.recv()
                hello_data = json.loads(hello_raw)
                # print(f"收到 Hello: {hello_data}")

                if hello_data.get("op") != 0:
                    raise ValueError("收到的第一个消息不是 Hello (Opcode 0)")

                obs_websocket_version = hello_data.get("d", {}).get("obsWebSocketVersion")
                rpc_version = hello_data.get("d", {}).get("rpcVersion", 1)
                authentication_required = hello_data.get("d", {}).get("authentication") is not None

                if authentication_required:
                    # 我们在 OBS 中禁用了身份验证，所以理论上不应该到这里
                    raise ConnectionAbortedError("OBS 需要身份验证，但脚本未配置密码！请在 OBS 中禁用身份验证或在脚本中配置。")

                # 3. 发送 Identify 消息 (Opcode 1)
                identify_payload = {
                    "op": 1,
                    "d": {
                        "rpcVersion": rpc_version
                        # 如果需要认证，这里要加 "authentication": "..."
                    }
                }
                temp_ws.send(json.dumps(identify_payload))
                # print(f"已发送 Identify (RPC Version: {rpc_version})")

                # 4. 接收 Identified 消息 (Opcode 2)
                identified_raw = temp_ws.recv()
                identified_data = json.loads(identified_raw)
                # print(f"收到 Identified: {identified_data}")

                if identified_data.get("op") != 2:
                    raise ValueError("收到的第二个消息不是 Identified (Opcode 2)")

                # --- 握手成功 --- #
                self.ws = temp_ws
                self.is_connected = True
                self.status_label.setText("已连接到 OBS (已识别)")
                self.connect_button.setText("断开连接")
                self.quick_start_button.setEnabled(True)  # 启用一键启动按钮
                # print(f"成功连接并识别 OBS WebSocket! (OBS v{obs_websocket_version}, RPC v{rpc_version})")
                self.refresh_media_sources()

            except websocket.WebSocketTimeoutException:
                if temp_ws: temp_ws.close()
                self.ws = None
                self.is_connected = False
                error_msg = "连接/握手超时: 请确保 OBS 已运行且 WebSocket 服务器已开启。"
                self.status_label.setText(error_msg)
                self.connect_button.setText("连接到 OBS")
                print(error_msg)
            except ConnectionRefusedError:
                if temp_ws: temp_ws.close()
                self.ws = None
                self.is_connected = False
                error_msg = "连接被拒绝: 请检查 OBS WebSocket 端口和设置。"
                self.status_label.setText(error_msg)
                self.connect_button.setText("连接到 OBS")
                print(error_msg)
            except ConnectionAbortedError as e:
                if temp_ws: temp_ws.close()
                self.ws = None
                self.is_connected = False
                error_msg = f"连接中止: {e}"
                self.status_label.setText(error_msg)
                self.connect_button.setText("连接到 OBS")
                print(error_msg)
            except Exception as e:
                if temp_ws: temp_ws.close()
                self.ws = None
                self.is_connected = False
                error_msg = f"连接/握手失败: {e}"
                self.status_label.setText(error_msg)
                self.connect_button.setText("连接到 OBS")
                print(error_msg)
        else:
            # 断开连接时禁用所有模块
            if self.speed_control["enabled"]: self.speed_checkbox.setChecked(False)
            if self.blur_control["enabled"]: self.blur_checkbox.setChecked(False)
            if self.transform_control["enabled"]: self.transform_checkbox.setChecked(False)
            if self.color_control["enabled"]: self.color_checkbox.setChecked(False)
            if self.audio_eq_control["enabled"]: self.audio_checkbox.setChecked(False)
            if self.compressor_control["enabled"]: self.compressor_enable_checkbox.setChecked(False)
            if self.gain_control["enabled"]: self.gain_enable_checkbox.setChecked(False)
            if self.audio_mute_control["enabled"]: self.audio_mute_checkbox.setChecked(False)
            if self.audio_volume_control["enabled"]: self.audio_volume_checkbox.setChecked(False)
            if self.plugin_dedup_control["enabled"]: self.plugin_dedup_checkbox.setChecked(False)

            # 重置一键启动状态
            self.is_batch_running = False
            self.active_functions = []
            self.quick_start_button.setText('一键启动')
            self.quick_start_button.setEnabled(False)  # 禁用一键启动按钮

            # 清空统一的媒体源下拉框
            self.video_source_combo.clear()
            self.video_source_combo.addItem("请先连接OBS并刷新", "")
            self.audio_source_combo.clear()
            # ... (重置状态)
            self.ws = None
            self.is_connected = False
            self.status_label.setText("未连接")
            self.connect_button.setText("连接到 OBS")
            # print("已断开与 OBS 的连接。")

    def get_next_request_id(self):
        """生成一个唯一的请求 ID。"""
        self.request_id_counter += 1
        return f"req-{int(time.time())}-{self.request_id_counter}"

    def send_request_and_get_response(self, request_type, request_data=None, timeout=2):
        """发送请求并等待接收对应的响应。返回响应的 'd' 部分或 None。"""
        if not self.is_connected or not self.ws:
            print("错误：尚未连接到 OBS，无法发送请求。")
            return None

        # 生成唯一的请求 ID
        request_id = self.get_next_request_id()

        payload = {
            "op": 6, # Request
            "d": {
                "requestType": request_type,
                "requestId": request_id,
                "requestData": request_data if request_data is not None else {}
            }
        }

        try:
            self.ws.send(json.dumps(payload))
            # print(f"已发送请求: {request_type}, ID: {request_id}") # 减少日志

            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response_raw = self.ws.recv()
                    response_data = json.loads(response_raw)

                    if response_data.get("op") == 7:
                        response_d = response_data.get("d", {})
                        if response_d.get("requestId") == request_id:
                            # --- 返回整个 'd' 部分 --- #
                            # print(f"收到响应 for ID {request_id}: {response_d}")
                            return response_d
                        # else: (忽略不匹配的响应)
                    # else: (忽略非响应消息)

                except websocket.WebSocketTimeoutException:
                    continue
                except json.JSONDecodeError:
                    print("错误：解析收到的 WebSocket 消息失败。")
                    continue # 尝试继续接收

            print(f"错误：等待对请求 {request_id} 的响应超时 ({timeout}秒)。")
            return None

        except websocket.WebSocketConnectionClosedException:
            print("错误：WebSocket 连接已关闭，无法发送或接收。")
            self.handle_disconnection()
            return None
        except Exception as e:
            print(f"发送或接收 WebSocket 时出错: {e}")
            self.handle_disconnection()
            return None

    def send_obs_request(self, request_type, request_data=None, callback=None):
        """向 OBS 发送 WebSocket 请求，并可选地注册回调函数。"""
        if not self.ws or not self.is_connected:
            print("错误：WebSocket 未连接，无法发送请求。")
            return None # 返回 None 表示发送失败

        self.request_id_counter += 1
        request_id = f"req-{int(time.time())}-{self.request_id_counter}"
        payload = {
            "op": 6, # Request
            "d": {
                "requestId": request_id,
                "requestType": request_type
            }
        }
        if request_data:
            payload["d"]["requestData"] = request_data

        # 存储请求信息，包括回调
        self.pending_requests[request_id] = {
            "type": request_type,
            "data": request_data,
            "callback": callback # 存储回调函数
        }

        try:
            self.ws.send(json.dumps(payload))
            # print(f"已发送请求 (ID: {request_id}): Type={request_type}, Data={request_data}") # 减少日志
            return request_id # 返回请求 ID，以便需要时可以跟踪
        except Exception as e:
            print(f"错误：发送请求 {request_id} ({request_type}) 失败: {e}")
            # 从待处理列表中移除失败的请求
            if request_id in self.pending_requests:
                del self.pending_requests[request_id]
            return None # 返回 None 表示发送失败

    def on_message(self, ws, message):
        """处理收到的 WebSocket 消息。"""
        try:
            data = json.loads(message)
            op = data.get("op")
            payload_data = data.get("d", {})

            if op == 0: # Hello
                # print(f"收到 Hello: {data}")
                self.identify_obs(payload_data)
            elif op == 2: # Identified
                # print(f"收到 Identified: {data}")
                # print(f"成功连接并识别 OBS WebSocket! (OBS v{self.connected_obs_info.get('version', '未知')}, RPC v{payload_data.get('negotiatedRpcVersion', '未知')})")
                self.is_connected = True
                self.status_label.setText("状态: 已连接")
                self.connect_button.setText("断开连接")
                self.quick_start_button.setEnabled(True)  # 启用一键启动按钮
                self.connected_obs_info['rpcVersion'] = payload_data.get('negotiatedRpcVersion')
                self.refresh_media_sources() # 连接成功后刷新源列表
            elif op == 5: # Event
                event_type = payload_data.get("eventType")
                event_data = payload_data.get("eventData", {})
                # print(f"收到事件: Type={event_type}, Data={event_data}") # 事件日志可选
                # 在这里处理你关心的事件，例如场景切换、源状态变化等
                if event_type == "CurrentProgramSceneChanged":
                    print(f"事件：当前场景已切换为 '{event_data.get('sceneName')}'")
                elif event_type == "MediaInputPlaybackEnded":
                     source_name = event_data.get('inputName')
                     print(f"事件：媒体源 '{source_name}' 播放结束")
                     # 如果加减速功能启用且是目标源，则触发速度调整
                     if self.speed_control["enabled"] and source_name == self.speed_control["source_name"]:
                         print(f"检测到受控媒体源 '{source_name}' 播放结束，准备应用新速度...")
                         # 确保 apply_new_speed_and_restart 正确处理结束状态
                         # 注意：这里可能需要延迟一点执行，以确保 OBS 状态完全更新
                         # QTimer.singleShot(100, lambda: self.apply_new_speed_and_restart(source_name))
                         # 或者直接调用，依赖 monitor_playback_state 的逻辑
                         pass # monitor_playback_state 会处理 OBS_MEDIA_STATE_ENDED

            elif op == 7: # RequestResponse
                request_id = payload_data.get("requestId")
                request_status = payload_data.get("requestStatus", {})
                response_data = payload_data.get("responseData")

                if request_id in self.pending_requests:
                    request_info = self.pending_requests.pop(request_id)
                    request_type = request_info["type"]
                    callback = request_info["callback"] # 获取存储的回调

                    if request_status.get("code") == 100: # 成功
                        # print(f"收到响应 for ID {request_id}: {data}") # 成功响应日志可选
                        # --- 如果有回调，调用回调 --- #
                        if callback:
                            try:
                                callback(response_data) # 将响应数据传递给回调
                            except Exception as e:
                                print(f"错误：执行请求 {request_id} ({request_type}) 的回调函数时出错: {e}")
                        # --- 如果没有回调，可以在这里添加默认处理逻辑 ---
                        # else:
                        #     # print(f"请求 {request_type} (ID: {request_id}) 成功完成，无特定回调。")
                        #     pass
                    else: # 失败
                        error_message = request_status.get("comment", "未知错误")
                        print(f"错误：请求 {request_id} ({request_type}) 失败: Code={request_status.get('code')}, Error='{error_message}'")
                        # --- 失败时也可以考虑调用回调，传递 None 或错误信息？ ---
                        # if callback:
                        #     try:
                        #         callback(None, error_message) # 可选：传递错误信息给回调
                        #     except Exception as e:
                        #         print(f"错误：执行请求 {request_id} ({request_type}) 的失败回调时出错: {e}")
                else:
                    print(f"警告：收到未知或已处理请求的响应 (ID: {request_id}): {data}")

            elif op == 9: # RequestBatchResponse
                print(f"收到批量请求响应: {data}")
                # 在这里处理批量响应
            else:
                print(f"收到未知操作码的消息: {message}")

        except json.JSONDecodeError:
            print(f"错误：无法解码收到的消息: {message}")
        except Exception as e:
            print(f"错误：处理消息时发生异常: {e}")
            import traceback
            traceback.print_exc() # 打印详细的堆栈跟踪

    def handle_disconnection(self):
        """处理意外断开连接的情况。"""
        print("检测到连接中断，正在清理...")
        # 主动调用断开逻辑来重置状态
        if self.is_connected:
             self.connect_to_obs() # 调用它来执行断开逻辑

    # --- 智能加减速模块逻辑 ---
    def refresh_media_sources(self, *args, **kwargs):
        """向 OBS 请求媒体源列表并更新视频和音频媒体源下拉框。"""
        if not self.is_connected:
             self.status_label.setText("请先连接到 OBS")
             # 清空下拉框并显示提示
             self.video_source_combo.clear()
             self.video_source_combo.addItem("请先连接并刷新", "")
             self.audio_source_combo.clear()
             self.audio_source_combo.addItem("请先连接并刷新", "")
             return

        # print("正在请求输入源列表...")
        response_d = self.send_request_and_get_response("GetInputList")

        # 清空所有下拉框
        self.video_source_combo.clear()
        self.audio_source_combo.clear()

        if response_d and response_d.get('requestStatus', {}).get('result') is True:
            inputs_list = response_d.get('responseData', {}).get('inputs')
            if inputs_list is not None:
                # print(f"成功获取到 {len(inputs_list)} 个输入源。")
                found_video = False
                found_audio = False

                # print("--- 开始筛选并填充下拉框 ---")
                for item in inputs_list:
                    input_name = item.get("inputName")
                    input_kind = item.get("inputKind")
                    if not input_name:
                        continue

                    # 计算各种标志位
                    is_video = bool(item.get("videoTracks"))
                    has_audio_tracks = item.get("audioTracks") is not None and len(item.get("audioTracks")) > 0
                    is_dedicated_audio = input_kind in [
                        "wasapi_input_capture", "wasapi_output_capture",
                        "coreaudio_input_capture", "coreaudio_output_capture",
                        "pulse_input_capture", "pulse_output_capture",
                        "jack_input_client", "alsa_input_capture"
                    ]
                    is_audio_capable = has_audio_tracks or is_dedicated_audio
                    is_media_source = (input_kind == 'ffmpeg_source' or input_kind == 'vlc_source')

                    display_name = f"{input_name} ({input_kind})"

                    # 填充视频媒体源下拉框 (媒体源 或 有视频轨道的源)
                    if is_media_source or is_video:
                        # print(f"  [视频兼容] 添加: {display_name}")
                        self.video_source_combo.addItem(display_name, input_name)
                        found_video = True

                    # 填充音频媒体源下拉框 (有音频能力的源 或 媒体源)
                    if is_audio_capable or is_media_source:
                        # print(f"  [音频兼容] 添加: {display_name}")
                        self.audio_source_combo.addItem(display_name, input_name)
                        found_audio = True

                # 更新下拉框状态
                if not found_video:
                    self.video_source_combo.addItem("未找到视频源", "")
                if not found_audio:
                    self.audio_source_combo.addItem("未找到音频源", "")

                # print("视频和音频媒体源下拉框已更新。")

            else:
                # print("获取输入源列表成功，但响应数据中未包含 'inputs' 字段。")
                self.video_source_combo.addItem("列表为空或无效", "")
                self.audio_source_combo.addItem("列表为空或无效", "")
        else:
            # print(f"刷新输入源列表失败或未收到有效响应。响应: {response_d}")
            self.video_source_combo.addItem("刷新失败", "")
            self.audio_source_combo.addItem("刷新失败", "")

    def update_video_source_for_all_functions(self, text):
        """当视频媒体源选择改变时，更新所有视频功能的媒体源设置"""
        source_name = self.video_source_combo.currentData() or ""
        if source_name:
            # 更新所有视频功能的媒体源设置
            self.speed_control["source_name"] = source_name
            self.blur_control["source_name"] = source_name
            self.transform_control["source_name"] = source_name
            self.color_control["source_name"] = source_name
            print(f"视频媒体源已更新为: {source_name}")

    def update_audio_source_for_all_functions(self, text):
        """当音频媒体源选择改变时，更新所有音频功能的媒体源设置"""
        source_name = self.audio_source_combo.currentData() or ""
        if source_name:
            # 更新所有音频功能的媒体源设置
            self.audio_eq_control["source_name"] = source_name
            self.audio_mute_control["source_name"] = source_name
            self.audio_volume_control["source_name"] = source_name
            # print(f"音频媒体源已更新为: {source_name}")

    def toggle_speed_control(self, state):
        """启用或禁用智能加减速功能。"""
        is_enabled = (state == Qt.Checked)
        self.speed_control["enabled"] = is_enabled
        # print(f"智能加减速功能已 {'启用' if is_enabled else '禁用'}")
        # --- 添加事件处理 --- #
        QApplication.processEvents()

        if is_enabled:
            selected_source = self.video_source_combo.currentData()
            if not selected_source:
                print("错误：请先选择一个视频媒体源！")
                # 避免递归调用，使用QTimer延迟设置
                QTimer.singleShot(10, lambda: self.speed_checkbox.setChecked(False))
                return
            self.speed_control["source_name"] = selected_source
            # 确保速度范围有效
            if self.speed_control["min_speed"] > self.speed_control["max_speed"]:
                self.speed_control["min_speed"], self.speed_control["max_speed"] = \
                    self.speed_control["max_speed"], self.speed_control["min_speed"]
                self.speed_min_spinbox.setValue(self.speed_control["min_speed"])
                self.speed_max_spinbox.setValue(self.speed_control["max_speed"])

            # 初始化状态
            self.last_known_cursor = 0
            self.stall_start_time = None

            # 启动定时器前，先立即应用一次
            # print("首次启动，立即应用速度并重启...")
            try:
                # --- 添加缺失的参数 --- #
                self.apply_new_speed_and_restart(self.speed_control["source_name"])
                # -------------------- #
            except Exception as e_apply:
                print(f"错误：首次应用速度时发生异常: {e_apply}")
                import traceback
                traceback.print_exc()
                # 首次失败，阻止启动，避免递归调用
                QTimer.singleShot(10, lambda: self.speed_checkbox.setChecked(False))
                return

            # 启动监控定时器
            interval_ms = self.speed_control["interval_ms"]
            self.speed_control["timer"].start(interval_ms)
            # print(f"速度监控定时器已启动，间隔 {interval_ms} ms")
        else:
            self.speed_control["timer"].stop()
            # print("速度监控定时器已停止")
            # (可选：停止时是否要恢复原始速度或状态？ 暂时不恢复)
            # --- 添加事件处理 (如果需要界面更新) --- #
            QApplication.processEvents()

    def update_speed_setting(self, key, value):
        """更新速度设置的值。"""
        self.speed_control[key] = value
        print(f"速度设置更新: {key} = {value}")
        # 立即保存设置到磁盘
        self.save_settings()
        # 如果定时器正在运行，且范围无效，则纠正并重启定时器逻辑可能需要，但暂时简化

    def monitor_playback_state(self, *args):
        """定时检查媒体播放状态，用于智能加减速。"""
        if not self.speed_control["enabled"] or not self.is_connected:
            return

        source_name = self.speed_control["source_name"]
        if not source_name:
            return

        try:
            # print(f"监控: 获取 '{source_name}' 状态...") # 减少日志
            response_d = self.send_request_and_get_response("GetMediaInputStatus", {
                "inputName": source_name
            }, timeout=0.5) # 使用较短超时

            # --- 修正响应处理 --- #
            if not response_d or response_d.get('requestStatus', {}).get('result') is not True:
                # print(f"监控: 获取 '{source_name}' 状态失败或超时。响应: {response_d}")
                # 获取状态失败，暂时不做处理，等待下次轮询
                return

            response_data = response_d.get("responseData", {})
            media_state = response_data.get("mediaState")
            raw_cursor = response_data.get("mediaCursor") # 可能是 None
            # -------------------- #

            if media_state is None:
                # print(f"监控: 未能从响应中获取 '{source_name}' 的 mediaState。")
                return

            # ... (后续逻辑基本不变，依赖 media_state 和 raw_cursor)
            # ... (判断 OBS_MEDIA_STATE_ENDED)
            # ... (判断卡顿)

            # 检查是否播放结束
            if media_state == "OBS_MEDIA_STATE_ENDED":
                print(f"监控: 检测到 '{source_name}' 播放结束。")
                self.last_known_cursor = 0 # 重置光标
                self.stall_start_time = None # 重置卡顿检测
                # --- 明确传递参数 --- #
                current_source = self.speed_control["source_name"] # 再次获取以防万一?
                self.apply_new_speed_and_restart(current_source)
                # -------------------- #
                return # 处理完结束状态，本次监控结束

            # 卡顿检测逻辑 (如果不是 PAUSED 或 PLAYING，不进行卡顿检测)
            if media_state not in ["OBS_MEDIA_STATE_PAUSED", "OBS_MEDIA_STATE_PLAYING"]:
                self.stall_start_time = None # 非播放状态，重置卡顿计时
                return

            # 确保 mediaCursor 是数字类型
            media_cursor = 0
            if isinstance(raw_cursor, (int, float)):
                media_cursor = raw_cursor
            else:
                # 如果 mediaCursor 不是数字（比如刚启动或已结束为 None），则无法判断卡顿
                self.stall_start_time = None
                # print(f"监控: '{source_name}' 的 mediaCursor 不是有效数字 ({raw_cursor})，无法检测卡顿。")
                return

            current_time = time.time()
            stall_threshold = self.speed_control["stall_threshold_secs"]

            if media_cursor == self.last_known_cursor:
                if self.stall_start_time is None:
                    # print(f"监控: 光标未变 ({media_cursor}), 开始计时卡顿...")
                    self.stall_start_time = current_time
                elif current_time - self.stall_start_time >= stall_threshold:
                    print(f"监控: 检测到 '{source_name}' 卡顿超过 {stall_threshold} 秒 (光标: {media_cursor})。")
                    self.last_known_cursor = 0 # 重置光标避免重复触发
                    self.stall_start_time = None # 重置卡顿检测
                    # --- 明确传递参数 --- #
                    current_source = self.speed_control["source_name"] # 再次获取
                    self.apply_new_speed_and_restart(current_source)
                    # -------------------- #
                    return
            else:
                # 光标变化，更新记录并重置卡顿计时
                # print(f"监控: 光标变化 {self.last_known_cursor} -> {media_cursor}")
                self.last_known_cursor = media_cursor
                self.stall_start_time = None

        except Exception as e:
            print(f"严重错误：在 monitor_playback_state 中发生未处理异常: {e}")
            # 考虑停止定时器？取决于错误是否可恢复
            # if self.speed_control["timer"].isActive():
            #     self.speed_control["timer"].stop()
            #     print("错误发生，已停止速度监控定时器。")

    def apply_new_speed_and_restart(self, source_name):
        """应用新的随机速度并重启媒体源。"""
        # --- 添加顶层 try-except --- #
        try:
            if not self.speed_control["enabled"] or not self.is_connected:
                return

            min_s = self.speed_control["min_speed"]
            max_s = self.speed_control["max_speed"]

            # 生成新速度
            new_speed = self.speed_control["current_speed"]
            if min_s == max_s:
                new_speed = min_s
            elif min_s < max_s:
                 while new_speed == self.speed_control["current_speed"]:
                    new_speed = random.randint(min_s, max_s)
            # else: min_s > max_s (用户设置错误, 但之前 toggle 里应该矫正了), 保险起见用 min_s
            #    new_speed = min_s

            # print(f"应用: 准备为源 '{source_name}' 设置新速度: {new_speed}% 并重启...")
            self.speed_control["current_speed"] = new_speed

            # 1. 发送 SetInputSettings 请求更新速度
            settings_data = {
                "inputName": source_name,
                "inputSettings": {"speed_percent": new_speed},
                "overlay": True
            }
            # print("应用: 正在发送 SetInputSettings...")
            self.send_obs_request("SetInputSettings", settings_data)
            # print("应用: SetInputSettings 请求已发送。")

            # 2. 发送 TriggerMediaInputAction 请求重启媒体
            action_data = {
                "inputName": source_name,
                "mediaAction": "OBS_WEBSOCKET_MEDIA_INPUT_ACTION_RESTART"
            }
            # print("应用: 正在发送 RESTART Action...")
            self.send_obs_request("TriggerMediaInputAction", action_data)
            # print("应用: RESTART Action 请求已发送。")

            # 重置卡顿检测状态
            self.speed_control["last_media_cursor"] = -1
            self.speed_control["last_check_time"] = time.time()
            self.speed_control["stall_check_start_time"] = time.time()
            # print(f"应用: 速度应用和重启流程完成。")

        except Exception as e:
            print(f"严重错误：在 apply_new_speed_and_restart 核心逻辑中发生未处理异常: {e}")
            # 考虑停止定时器？
            # if self.speed_control["timer"].isActive():
            #     self.speed_control["timer"].stop()
            #     print("错误发生，已停止速度监控定时器。")

    # --- 模糊去重模块逻辑 ---
    def update_blur_setting(self, key, value):
        """更新模糊设置的值。"""
        if key == 'filter_name' and not value:
            print("警告：模糊滤镜名称不能为空！")
            value = "Composite Blur"
            self.blur_filter_name_edit.setText(value)

        self.blur_control[key] = value
        print(f"模糊设置更新: {key} = {value}")
        # 立即保存设置到磁盘
        self.save_settings()

        # --- 添加间隔更新逻辑 --- #
        if key == 'interval_ms' and self.blur_control["timer"].isActive():
            print("检测到模糊间隔变化且定时器运行中，正在重启定时器...")
            self.blur_control["timer"].stop()
            new_interval = max(100, value) # 保证最小间隔
            self.blur_control["timer"].start(new_interval)
            print(f"模糊控制定时器已使用新间隔 {new_interval} ms 重新启动。")
        # --------------------- #

    def toggle_blur_control(self, state):
        """启用或禁用模糊去重功能。"""
        is_enabled = (state == Qt.Checked)
        self.blur_control["enabled"] = is_enabled
        print(f"模糊去重功能已 {'启用' if is_enabled else '禁用'}")
        # --- 添加事件处理 --- #
        QApplication.processEvents()

        # 使用统一的视频媒体源
        source = self.video_source_combo.currentData()
        if source:
            self.blur_control["source_name"] = source
        else:
            source = self.blur_control["source_name"]

        filter_name = self.blur_control["filter_name"]

        if not source:
            print("错误：请先选择一个视频媒体源！")
            self.blur_checkbox.setChecked(False)
            QApplication.processEvents()
            return
        if not filter_name:
            print("错误：模糊滤镜名称不能为空！")
            self.blur_checkbox.setChecked(False)
            # --- 添加事件处理 --- #
            QApplication.processEvents()
            return

        if is_enabled:
            # 1. 确保滤镜存在并保存原始值
            filter_ok = self.ensure_blur_filter_exists_and_save_original(source, filter_name)
            if not filter_ok:
                print("错误：无法确保模糊滤镜存在或获取设置。")
                self.blur_checkbox.setChecked(False)
                # --- 添加事件处理 --- #
                QApplication.processEvents()
                return

            # 2. 确保半径范围有效
            if self.blur_control["min_radius"] > self.blur_control["max_radius"]:
                self.blur_control["min_radius"], self.blur_control["max_radius"] = \
                    self.blur_control["max_radius"], self.blur_control["min_radius"]
                self.blur_min_radius_spin.setValue(self.blur_control["min_radius"])
                self.blur_max_radius_spin.setValue(self.blur_control["max_radius"])

            # 3. 启动定时器并立即执行一次
            interval = max(100, self.blur_control["interval_ms"]) # 保证最小间隔
            self.blur_control["timer"].start(interval)
            # print(f"模糊控制定时器已启动，间隔 {interval} ms")
            self.execute_blur_change()

        else:
            # 停止定时器并恢复原始值
            self.blur_control["timer"].stop()
            # print("模糊控制定时器已停止")
            self.restore_original_blur_radius(source, filter_name)
            # --- 自动删除滤镜 --- #
            self.remove_blur_filter(source, filter_name)
            # --- 添加事件处理 --- #
            QApplication.processEvents()

    def remove_blur_filter(self, source_name, filter_name):
        """移除指定的模糊滤镜。"""
        print(f"尝试移除模糊滤镜 '{filter_name}' from '{source_name}'...")
        self.send_obs_request("RemoveSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })
        print(f"移除模糊滤镜 '{filter_name}' 的请求已发送。")

    def ensure_blur_filter_exists_and_save_original(self, source_name, filter_name):
        """检查模糊滤镜是否存在。如果存在则保存原始半径，不存在则自动创建。"""
        print(f"检查滤镜 '{filter_name}' on '{source_name}'...")
        filter_info_d = self.send_request_and_get_response("GetSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })

        if filter_info_d and filter_info_d.get('requestStatus', {}).get('result') is True:
            print("滤镜已存在。正在获取并保存原始半径...")
            settings = filter_info_d.get("responseData", {}).get("filterSettings", {})
            # --- 使用正确的参数名 'radius' --- #
            original_radius = settings.get("radius", 0.0)
            self.blur_control["original_radius"] = original_radius
            print(f"原始模糊半径 ('radius') 已保存: {original_radius}")
            return True
        else:
            print(f"模糊滤镜 '{filter_name}' 不存在，尝试创建 (类型: obs_composite_blur)...")
            filter_kind = "obs_composite_blur"  # Composite Blur kind id
            default_settings = {"radius": 0.0}  # 可根据需要扩展默认参数
            creation_response_d = self.send_request_and_get_response("CreateSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterKind": filter_kind,
                "filterSettings": default_settings
            })
            if creation_response_d and creation_response_d.get('requestStatus', {}).get('result') is True:
                print(f"成功创建模糊滤镜 '{filter_name}'")
                # 创建后激活滤镜，强制刷新，避免黑屏
                self.send_request_and_get_response("SetSourceFilterSettings", {
                    "sourceName": source_name,
                    "filterName": filter_name,
                    "filterSettings": {"radius": 0.01},
                    "overlay": True
                })
                self.send_request_and_get_response("SetSourceFilterSettings", {
                    "sourceName": source_name,
                    "filterName": filter_name,
                    "filterSettings": {"radius": 0.0},
                    "overlay": True
                })
                # 创建后再次获取并保存原始半径
                filter_info_d = self.send_request_and_get_response("GetSourceFilter", {
                    "sourceName": source_name,
                    "filterName": filter_name
                })
                if filter_info_d and filter_info_d.get('requestStatus', {}).get('result') is True:
                    settings = filter_info_d.get("responseData", {}).get("filterSettings", {})
                    original_radius = settings.get("radius", 0.0)
                    self.blur_control["original_radius"] = original_radius
                    print(f"原始模糊半径 ('radius') 已保存: {original_radius}")
                return True
            else:
                print(f"错误：创建模糊滤镜 '{filter_name}' (类型: {filter_kind}) 失败！(响应: {creation_response_d})")
                return False

    def restore_original_blur_radius(self, source_name, filter_name):
        """恢复模糊滤镜到原始半径。"""
        original_radius = self.blur_control["original_radius"]
        print(f"尝试恢复滤镜 '{filter_name}' on '{source_name}' 到半径: {original_radius}")
        settings_data = {
            "sourceName": source_name, # 确保有 sourceName
            "filterName": filter_name,
             # --- 使用正确的参数名 'radius' --- #
            "filterSettings": {"radius": original_radius},
            "overlay": True # 合并可能比覆盖更安全
        }
        response_d = self.send_request_and_get_response("SetSourceFilterSettings", settings_data, timeout=1)
        if response_d and response_d.get('requestStatus', {}).get('result') is True:
            print("成功恢复原始模糊半径。")
        else:
            print("警告：恢复原始模糊半径失败或超时。")

    def execute_blur_change(self, *args):
        """执行一次模糊半径变更。"""
        if not self.blur_control["enabled"] or not self.is_connected:
            return

        min_r = self.blur_control["min_radius"]
        max_r = self.blur_control["max_radius"]
        source = self.blur_control["source_name"]
        filter_name = self.blur_control["filter_name"]

        # 生成随机半径
        new_radius = min_r
        if max_r > min_r:
            new_radius = random.uniform(min_r, max_r)

        print(f"模糊: 为源 '{source}' 滤镜 '{filter_name}' 设置新半径 ('radius'): {new_radius:.2f}")

        settings_data = {
            "sourceName": source,
            "filterName": filter_name,
             # --- 使用正确的参数名 'radius' --- #
            "filterSettings": {"radius": new_radius},
            "overlay": True
        }
        self.send_obs_request("SetSourceFilterSettings", settings_data)

    # --- 视频移动去重模块逻辑 ---
    def update_transform_setting(self, key, value):
        """更新移动变换设置的值。"""
        self.transform_control[key] = value
        print(f"移动变换设置更新: {key} = {value}")
        # 立即保存设置到磁盘
        self.save_settings()
        # --- 修复: 修改间隔时需要 stop() 和 start() 定时器 --- #
        if key == 'interval_secs' and self.transform_control["timer"].isActive():
            print("检测到移动间隔变化且定时器运行中，正在重启定时器...")
            self.transform_control["timer"].stop()
            interval_ms = max(100, int(value * 1000)) # 计算新的毫秒间隔
            # self.transform_control["timer"].setInterval(interval_ms) # 设置新间隔不是必须的，start()会处理
            self.transform_control["timer"].start(interval_ms)
            print(f"移动变换定时器已使用新间隔 {interval_ms} ms 重新启动。")
        # ---------------------------------------------------- #

    def toggle_transform_control(self, state):
        """启用或禁用视频移动去重功能"""
        is_enabled = (state == Qt.Checked)
        self.transform_control["enabled"] = is_enabled
        print(f"视频移动去重功能已 {'启用' if is_enabled else '禁用'}")
        QApplication.processEvents()

        # 使用统一的视频媒体源
        source_name = self.video_source_combo.currentData()
        if source_name:
            self.transform_control["source_name"] = source_name
        else:
            source_name = self.transform_control["source_name"]

        if not source_name:
            print("错误：请先选择一个视频媒体源！")
            self.transform_checkbox.setChecked(False)
            QApplication.processEvents()
            return

        if is_enabled:
            # 先获取画布分辨率，确保适配任意分辨率
            self.get_output_dimensions()
            # 重置到中心位置并开始动画
            self.reset_to_center()
            # 启动定时器
            timer_interval_ms = 200  # 每200ms检查一次状态
            self.transform_control["timer"].start(timer_interval_ms)
            # print(f"变换: 状态检查定时器已启动，间隔 {timer_interval_ms} ms")

        else:
            # 停止定时器
            self.transform_control["timer"].stop()
            # print("变换: 状态检查定时器已停止")
            # 恢复媒体源到画布大小
            scene_name = self.get_current_scene_name()
            item_id = self.get_scene_item_id(source_name)
            if scene_name and item_id:
                transform = self.get_original_transform(scene_name, item_id)
                if transform:
                    source_w = transform.get("sourceWidth", self.transform_control["canvas_width"])
                    source_h = transform.get("sourceHeight", self.transform_control["canvas_height"])
                    canvas_w = self.transform_control["canvas_width"]
                    canvas_h = self.transform_control["canvas_height"]
                    # 计算缩放比例，保证填满画布
                    scale = max(canvas_w / source_w, canvas_h / source_h)
                    scaled_w = source_w * scale
                    scaled_h = source_h * scale
                    pos_x = (canvas_w - scaled_w) / 2
                    pos_y = (canvas_h - scaled_h) / 2
                    self.send_obs_request("SetSceneItemTransform", {
                        "sceneName": scene_name,
                        "sceneItemId": item_id,
                        "sceneItemTransform": {
                            "positionX": pos_x,
                            "positionY": pos_y,
                            "scaleX": scale,
                            "scaleY": scale
                        }
                    })
            QApplication.processEvents()

    def get_current_scene_and_item_id(self, source_name):
        """获取当前场景名称和指定源的场景项 ID。"""
        print(f"正在获取当前场景...")
        scene_response_d = self.send_request_and_get_response("GetCurrentProgramScene")
        if not scene_response_d or scene_response_d.get('requestStatus', {}).get('result') is not True:
            print("错误：无法获取当前场景名称。")
            return None
        scene_name = scene_response_d.get('responseData', {}).get("currentProgramSceneName")
        if not scene_name: # 额外检查一下 scene_name 是否真的存在
            print("错误：无法从响应中获取 currentProgramSceneName。")
            return None

        print(f"正在场景 '{scene_name}' 中查找源 '{source_name}' 的 ID...")
        item_id_response_d = self.send_request_and_get_response("GetSceneItemId", {
            "sceneName": scene_name,
            "sourceName": source_name
        })
        if not item_id_response_d or item_id_response_d.get('requestStatus', {}).get('result') is not True:
            print(f"错误：在场景 '{scene_name}' 中未找到源 '{source_name}'！")
            return None
        item_id = item_id_response_d.get('responseData', {}).get("sceneItemId")
        if item_id is None: # 额外检查
             print(f"错误：无法从响应中获取 sceneItemId。")
             return None
        print(f"找到场景项 ID: {item_id}")
        return {"sceneName": scene_name, "sceneItemId": item_id}

    def get_original_transform(self, scene_name, item_id):
        """获取指定场景项的原始变换信息。"""
        print(f"正在获取场景项 {item_id} 的原始变换...")
        transform_response_d = self.send_request_and_get_response("GetSceneItemTransform", {
            "sceneName": scene_name,
            "sceneItemId": item_id
        })
        if transform_response_d and transform_response_d.get('requestStatus', {}).get('result') is True:
            transform_data = transform_response_d.get('responseData', {}).get("sceneItemTransform")
            if transform_data:
                 return transform_data
        print("错误：获取原始变换信息失败。")
        return None

    def restore_original_transform(self):
        """恢复场景项到原始变换状态。"""
        if not self.transform_control["original_transform"]:
            print("警告：没有可恢复的原始变换信息。")
            return

        scene_name = self.transform_control["scene_name"]
        item_id = self.transform_control["scene_item_id"]
        original_tf = self.transform_control["original_transform"]

        # 使用原始变换数据，但过渡时间设为 0 或很小的值
        transform_data = original_tf.copy()
        transition_ms = 50 # 快速恢复，比如 50ms

        print(f"尝试恢复场景项 {item_id} 到原始变换 (过渡: {transition_ms}ms)...")
        self.send_obs_request("SetSceneItemTransform", {
            "sceneName": scene_name,
            "sceneItemId": item_id,
            "sceneItemTransform": transform_data,
            "sceneItemTransitionDuration": transition_ms
        })

    # --- 恢复：生成目标并启动 OBS 过渡的函数 --- #
    def start_new_transform_animation(self):
        """生成新的随机目标并启动到该目标的 OBS 平滑动画。"""
        state = self.transform_control
        if not state["enabled"] or not self.is_connected or state['scene_item_id'] < 0:
            return

        # 更新安全参数以防UI修改
        self.calculate_transform_safe_params()

        original_pos_x = state["original_transform"].get("positionX", 0)
        original_pos_y = state["original_transform"].get("positionY", 0)

        # 1. 生成随机目标缩放 (百分比转为小数)
        min_s = state["fixed_scale"] / 100.0
        max_s = state["fixed_scale"] / 100.0
        target_scale = random.uniform(min_s, max_s) if max_s > min_s else min_s

        # 2. 生成随机目标位置 (在安全范围内基于原始位置偏移)
        dx = random.uniform(-state['safe_move_x'], state['safe_move_x']) if state['safe_move_x'] > 0 else 0
        dy = random.uniform(-state['safe_move_y'], state['safe_move_y']) if state['safe_move_y'] > 0 else 0
        target_pos_x = original_pos_x + dx
        target_pos_y = original_pos_y + dy

        # 3. 计算 OBS 过渡时间 (ms)
        transition_duration_ms = max(100, int(state["transition_secs"] * 1000))

        print(f"变换: 开始新 OBS 动画 -> TargetScale={target_scale:.3f}, TargetPos=({target_pos_x:.0f}, {target_pos_y:.0f}), Duration={transition_duration_ms}ms")

        # 4. 准备变换数据 (只需目标状态，移除合并)
        new_transform = {
            "positionX": target_pos_x,
            "positionY": target_pos_y,
            "scaleX": target_scale,
            "scaleY": target_scale,
             # --- 移除合并逻辑 --- #
            # **{k: v for k, v in state["original_transform"].items() if k not in ['positionX', 'positionY', 'scaleX', 'scaleY', 'sourceWidth', 'sourceHeight', 'width', 'height']}
            # ------------------- #
        }

        # 5. 发送带过渡时间的变换请求
        self.send_obs_request("SetSceneItemTransform", {
            "sceneName": state["scene_name"],
            "sceneItemId": state["scene_item_id"],
            "sceneItemTransform": new_transform,
            "sceneItemTransitionDuration": transition_duration_ms
        })

        # 6. 更新动画状态
        state["is_animating"] = True
        state["animation_start_time"] = time.time()


    # --- 恢复：检查动画状态的定时器回调 --- #
    def check_transform_animation_status(self, *args):
        """定时器回调：检查当前 OBS 动画是否完成，并在需要时启动新动画。"""
        state = self.transform_control
        if not state["enabled"] or not self.is_connected or state['scene_item_id'] < 0:
            return

        current_time = time.time()

        if state["is_animating"]:
            # 检查当前动画理论上是否已完成
            elapsed_time = current_time - state["animation_start_time"]
            transition_secs = state["transition_secs"]
            if elapsed_time >= transition_secs:
                # print(f"变换: 动画理论上完成 (Elapsed={elapsed_time:.2f}s >= Transition={transition_secs:.2f}s)")
                state["is_animating"] = False
                state["last_animation_end_time"] = current_time # 记录结束时间
            # else: 动画进行中，等待下次检查
        else:
            # 如果不在动画中，检查是否过了等待间隔
            wait_time_elapsed = current_time - state["last_animation_end_time"]
            interval_secs = state["interval_secs"]
            if wait_time_elapsed >= interval_secs:
                print(f"变换: 等待间隔 ({interval_secs:.1f}s) 已过，准备启动新动画。")
                self.start_new_transform_animation() # 启动新的 OBS 动画
            # else: 等待间隔还没到，继续等待

    # --- 新增：获取 OBS 输出/画布尺寸 ---
    def get_output_dimensions(self):
        """获取 OBS 输出（画布）的宽度和高度。"""
        def on_response(data):
            if data and data.get('outputWidth') and data.get('outputHeight'):
                self.transform_control['canvas_width'] = data['outputWidth']
                self.transform_control['canvas_height'] = data['outputHeight']
                print(f"变换: 获取到画布尺寸: {self.transform_control['canvas_width']}x{self.transform_control['canvas_height']}")
            else:
                print(f"错误: 获取画布尺寸失败或数据无效: {data}")

        self.send_obs_request("GetVideoSettings", {}, callback=on_response)

    # --- 新增：获取源的原始尺寸 ---
    def get_source_dimensions(self, source_name):
        """获取指定媒体源的原始宽度和高度。"""
        def on_response(data):
            if data and data.get('sourceWidth') and data.get('sourceHeight'):
                self.transform_control['original_width'] = data['sourceWidth']
                self.transform_control['original_height'] = data['sourceHeight']
                print(f"变换: 获取到源 {source_name} 原始尺寸: {self.transform_control['original_width']}x{self.transform_control['original_height']}")
                # 获取尺寸后，计算安全参数
                self.calculate_transform_safe_params()
            else:
                print(f"错误: 获取源 {source_name} 尺寸失败或数据无效: {data}")
                # 出错时禁用功能？
                self.transform_checkbox.setChecked(False)
                self.transform_control["enabled"] = False
                QApplication.processEvents()

        self.send_obs_request("GetInputSettings", {"inputName": source_name}, callback=on_response)
        # 注意：GetInputSettings 可能不直接返回尺寸，取决于源类型。
        # 备选方案：可能需要 GetSceneItemList 获取 sceneItemId，然后 GetSceneItemProperties 获取 bounds。
        # 或者在获取原始 transform 时尝试获取 width/height （如果 OBS WebSocket 提供的话）
        # 临时简化：假设 transform_control['original_transform'] 中有 width/height (需要验证)
        # if self.transform_control['original_transform'].get('sourceWidth') and self.transform_control['original_transform'].get('sourceHeight'):
        #     self.transform_control['original_width'] = self.transform_control['original_transform']['sourceWidth']
        #     self.transform_control['original_height'] = self.transform_control['original_transform']['sourceHeight']
        #     self.calculate_transform_safe_params()
        # else:
        #     print("警告：无法从原始变换信息中获取源尺寸，安全范围计算可能不准确。")
        #     # 如果无法获取，可能需要禁用或给出警告

    # --- 新增：计算安全移动参数 --- (移植自原脚本)
    def calculate_transform_safe_params(self):
        """根据当前设置和尺寸计算安全的移动范围。"""
        state = self.transform_control
        # --- 增加日志：显示输入参数 ---
        print(f"变换: 开始计算安全参数 - 源尺寸: {state['original_width']}x{state['original_height']}, 画布: {state['canvas_width']}x{state['canvas_height']}, 用户范围 X:±{state['safe_move_x']}, Y:±{state['safe_move_y']}, 最小缩放:{state['fixed_scale']}%" )

        if not (state['original_width'] > 0 and state['original_height'] > 0 and state['canvas_width'] > 0 and state['canvas_height'] > 0):
            print("警告: 计算安全参数所需尺寸信息不完整 (存在0或未设置)，跳过计算。安全范围将为0。")
            state['safe_move_x'] = 0
            state['safe_move_y'] = 0
            return

        # ... (后续计算逻辑不变: 调整缩放范围, 计算覆盖所需缩放, 计算安全范围)
        # 1. 确保最小缩放 >= 100%
        state["fixed_scale"] = max(100, state["fixed_scale"])
        self.transform_min_scale_spin.setValue(state["fixed_scale"]) # 更新UI

        # 2. 确保最大缩放不超过 200%
        state["fixed_scale"] = min(200, state["fixed_scale"])
        self.transform_max_scale_spin.setValue(state["fixed_scale"]) # 更新UI

        # 3. 确保最小 <= 最大
        if state["fixed_scale"] > state["fixed_scale"]:
            state["fixed_scale"], state["fixed_scale"] = state["fixed_scale"], state["fixed_scale"]
            self.transform_min_scale_spin.setValue(state["fixed_scale"])
            self.transform_max_scale_spin.setValue(state["fixed_scale"])

        # 4. 计算覆盖画布所需的最小缩放（如果需要完全覆盖）
        min_scale_x_req = state['canvas_width'] / state['original_width']
        min_scale_y_req = state['canvas_height'] / state['original_height']
        required_min_scale_for_cover = max(min_scale_x_req, min_scale_y_req) * 100

        # 5. 结合用户设置的最小缩放和覆盖需求
        effective_min_scale_percent = max(state["fixed_scale"], required_min_scale_for_cover)
        effective_min_scale = effective_min_scale_percent / 100.0

        # 6. 计算基于 effective_min_scale 的安全移动范围
        safe_x = max(0, (effective_min_scale * state['original_width'] - state['canvas_width']) / 2)
        safe_y = max(0, (effective_min_scale * state['original_height'] - state['canvas_height']) / 2)

        # 7. 结合用户设置的移动范围和计算出的安全范围
        state['safe_move_x'] = min(state['safe_move_x'], safe_x)
        state['safe_move_y'] = min(state['safe_move_y'], safe_y)

        # --- 增加日志：显示计算结果 ---
        print(f"变换: 计算安全参数完成: 理论最小缩放={effective_min_scale_percent:.1f}%, 安全移动 X:±{state['safe_move_x']:.0f}px, Y:±{state['safe_move_y']:.0f}px")

    # --- 颜色去重模块逻辑 ---
    def update_color_setting(self, key, value):
        """更新颜色设置的值，并校验范围，处理间隔更新。"""
        self.color_control[key] = value
        print(f"颜色设置更新: {key} = {value}")
        # 立即保存设置到磁盘
        self.save_settings()

        # 校验 Min/Max
        if key.startswith('min_'):
            param = key[4:]
            max_key = f'max_{param}'
            if value > self.color_control.get(max_key, value):
                print(f"警告：{param} 的最小值 ({value}) 不能大于最大值 ({self.color_control.get(max_key)})。自动调整最大值。")
                self.color_control[max_key] = value
                # 更新对应的 Max SpinBox (如果存在)
                max_spin = getattr(self, f"color_{param}_max_spin", None)
                if max_spin: max_spin.setValue(value)
        elif key.startswith('max_'):
            param = key[4:]
            min_key = f'min_{param}'
            if value < self.color_control.get(min_key, value):
                print(f"警告：{param} 的最大值 ({value}) 不能小于最小值 ({self.color_control.get(min_key)})。自动调整最小值。")
                self.color_control[min_key] = value
                min_spin = getattr(self, f"color_{param}_min_spin", None)
                if min_spin: min_spin.setValue(value)

        # --- 添加间隔更新逻辑 --- #
        if key == 'interval_ms' and self.color_control["timer"].isActive():
            print("检测到间隔变化且定时器运行中，正在重启定时器...")
            self.color_control["timer"].stop()
            new_interval = max(100, value) # 保证最小间隔
            self.color_control["timer"].start(new_interval)
            print(f"颜色控制定时器已使用新间隔 {new_interval} ms 重新启动。")
        # --------------------- #

    def toggle_color_control(self, state):
        """启用或禁用颜色去重功能 (基于原脚本逻辑: 创建/移除滤镜)。"""
        is_enabled = (state == Qt.Checked)
        self.color_control["enabled"] = is_enabled
        print(f"颜色去重功能已 {'启用' if is_enabled else '禁用'}")
        # --- 强制处理事件，更新复选框外观 --- #
        QApplication.processEvents()

        # 使用统一的视频媒体源
        source = self.video_source_combo.currentData()
        if source:
            self.color_control["source_name"] = source
        else:
            source = self.color_control["source_name"]

        filter_name = self.color_control["filter_name"]

        if not source:
            print("错误：请先选择一个视频媒体源！")
            self.color_checkbox.setChecked(False)
            QApplication.processEvents()
            return
        if not filter_name:
            print("错误：颜色滤镜名称不能为空！")
            self.color_checkbox.setChecked(False)
            QApplication.processEvents() # 更新复选框状态
            return

        if is_enabled:
            filter_ok = self.ensure_color_filter_exists(source, filter_name)
            # --- 在耗时操作后再次处理事件 --- #
            QApplication.processEvents()
            if not filter_ok:
                self.color_checkbox.setChecked(False)
                QApplication.processEvents()
                return

            interval = max(100, self.color_control["interval_ms"])
            self.color_control["timer"].start(interval)
            # print(f"颜色控制定时器已启动，间隔 {interval} ms")
            self.execute_color_change()
        else:
            self.color_control["timer"].stop()
            # print("颜色控制定时器已停止")
            self.remove_color_filter(source, filter_name)
            # --- 移除操作后处理事件 --- #
            QApplication.processEvents()

    def ensure_color_filter_exists(self, source_name, filter_name):
        """检查颜色滤镜是否存在，不存在则创建。"""
        print(f"检查滤镜 '{filter_name}' on '{source_name}'...")
        filter_info_d = self.send_request_and_get_response("GetSourceFilter", {
            "sourceName": source_name, "filterName": filter_name })

        if filter_info_d and filter_info_d.get('requestStatus', {}).get('result') is True:
            print("颜色滤镜已存在。")
            # 检查类型是否正确 (可选)
            expected_kind = "color_filter"
            if filter_info_d.get("responseData", {}).get("filterKind") != expected_kind:
                 print(f"警告：找到名为 '{filter_name}' 的滤镜，但其类型 ({filter_info_d.get('responseData', {}).get('filterKind')}) 不是预期的 '{expected_kind}'！后续操作可能失败。")
            return True
        else:
            print(f"颜色滤镜 '{filter_name}' 不存在，尝试创建 (类型: color_filter)...")
            filter_kind = "color_filter" # <-- 使用 color_filter Kind ID
            default_values = {"hue_shift": 0, "brightness": 0, "contrast": 1, "saturation": 1, "gamma": 1}
            creation_response_d = self.send_request_and_get_response("CreateSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterKind": filter_kind,
                "filterSettings": default_values # 使用无效果的默认值创建
            })
            if creation_response_d and creation_response_d.get('requestStatus', {}).get('result') is True:
                print(f"成功创建颜色滤镜 '{filter_name}'")
                return True
            else:
                print(f"错误：创建颜色滤镜 '{filter_name}' 失败！(响应: {creation_response_d})") # 打印响应帮助调试
                return False

    def remove_color_filter(self, source_name, filter_name):
        """移除指定的颜色滤镜。"""
        print(f"尝试移除滤镜 '{filter_name}' from '{source_name}'...")
        # 使用不需要响应的函数，移除操作通常不需要确认
        self.send_obs_request("RemoveSourceFilter", {
            "sourceName": source_name,
            "filterName": filter_name
        })
        # 这里可以加个小延迟再打印日志，确保命令已发送
        print(f"移除滤镜 '{filter_name}' 的请求已发送。")

    # ... (音频逻辑)

    # --- 音频去重模块逻辑 ---
    def update_audio_eq_setting(self, key, value):
        """更新音频 EQ 设置的值，并校验范围，处理间隔。"""
        self.audio_eq_control[key] = value
        print(f"音频 EQ 设置更新: {key} = {value}")
        # 立即保存设置到磁盘
        self.save_settings()

        # 校验 Min/Max
        if key.startswith('min_'):
            param = key[4:] # e.g., low_gain
            max_key = f'max_{param}'
            if value > self.audio_eq_control.get(max_key, value):
                print(f"警告：{param} 的最小值 ({value:.1f}) 不能大于最大值 ({self.audio_eq_control.get(max_key):.1f})。自动调整最大值。")
                self.audio_eq_control[max_key] = value
                max_spin = getattr(self, f"audio_{param.split('_')[0]}_max_spin", None)
                if max_spin: max_spin.setValue(value)
        elif key.startswith('max_'):
            param = key[4:]
            min_key = f'min_{param}'
            if value < self.audio_eq_control.get(min_key, value):
                print(f"警告：{param} 的最大值 ({value:.1f}) 不能小于最小值 ({self.audio_eq_control.get(min_key):.1f})。自动调整最小值。")
                self.audio_eq_control[min_key] = value
                min_spin = getattr(self, f"audio_{param.split('_')[0]}_min_spin", None)
                if min_spin: min_spin.setValue(value)

        # --- 修复间隔更新逻辑 --- #
        if key == 'interval_secs' and self.audio_eq_control["timer"].isActive():
            print("检测到音频 EQ 间隔变化且定时器运行中，正在重启定时器...")
            self.audio_eq_control["timer"].stop()
            interval_ms = max(100, int(value * 1000)) # 计算新的毫秒间隔
            self.audio_eq_control["timer"].start(interval_ms)
            print(f"音频 EQ 定时器已使用新间隔 {interval_ms} ms 重新启动。")
        # --------------------- #

    def toggle_audio_eq_control(self, state):
        """启用或禁用音频 EQ 去重功能 (假设滤镜已存在)。"""
        is_enabled = (state == Qt.Checked)
        self.audio_eq_control["enabled"] = is_enabled
        print(f"音频 EQ 去重功能已 {'启用' if is_enabled else '禁用'}")
        # --- 添加事件处理 --- #
        QApplication.processEvents()

        # 使用统一的音频媒体源
        source = self.audio_source_combo.currentData()
        if source:
            self.audio_eq_control["source_name"] = source
        else:
            source = self.audio_eq_control["source_name"]

        filter_name = self.audio_eq_control["filter_name"]

        if not source:
            print("错误：请先选择一个音频媒体源！")
            self.audio_checkbox.setChecked(False)
            QApplication.processEvents()
            return
        if not filter_name:
            print("错误：音频 EQ 滤镜名称不能为空！")
            self.audio_checkbox.setChecked(False)
            # --- 添加事件处理 --- #
            QApplication.processEvents()
            return

        if is_enabled:
            # 1. 检查滤镜是否存在并保存原始值
            filter_ok = self.ensure_audio_eq_filter_exists_and_save_original(source, filter_name)
            if not filter_ok:
                self.audio_checkbox.setChecked(False); return

            # 2. 启动定时器并立即执行一次
            interval_ms = max(100, int(self.audio_eq_control["interval_secs"] * 1000))
            # self.audio_eq_control["timer"].setInterval(interval_ms) # start会设置
            self.audio_eq_control["timer"].start(interval_ms)
            print(f"音频 EQ 定时器已启动，间隔 {interval_ms} ms")
            self.execute_audio_eq_change()
        else:
            # 停止定时器并恢复原始值
            self.audio_eq_control["timer"].stop()
            print("音频 EQ 定时器已停止")
            self.restore_original_audio_gains(source, filter_name)
            # --- 添加事件处理 --- #
            QApplication.processEvents()

    def ensure_audio_eq_filter_exists_and_save_original(self, source_name, filter_name):
        """检查三段 EQ 滤镜是否存在，不存在则创建。保存原始增益。"""
        print(f"检查 EQ 滤镜 '{filter_name}' on '{source_name}'...")
        filter_info_d = self.send_request_and_get_response("GetSourceFilter", {
            "sourceName": source_name, "filterName": filter_name })

        eq_params = ["low", "mid", "high"]
        default_values = {"low": 0.0, "mid": 0.0, "high": 0.0}
        original_settings = {}

        if filter_info_d and filter_info_d.get('requestStatus', {}).get('result') is True:
            # ... (滤镜已存在的逻辑不变: 保存原始值, 打印 Kind ID)
            print("EQ 滤镜已存在。正在获取并保存原始增益...")
            response_data = filter_info_d.get("responseData", {})
            settings = response_data.get("filterSettings", {})
            actual_kind = response_data.get("filterKind")
            print(f"找到的滤镜类型 (Kind ID): {actual_kind}")
            for param in eq_params:
                original_settings[param] = settings.get(param, default_values[param])
            self.audio_eq_control["original_gains"] = original_settings
            print(f"原始 EQ 增益已保存: {original_settings}")
            return True
        else:
            # --- 重新加入创建逻辑，使用正确的 Kind ID --- #
            print(f"EQ 滤镜 '{filter_name}' 不存在，尝试创建 (类型: basic_eq_filter)...")
            filter_kind = "basic_eq_filter" # <-- 使用正确的 Kind ID!
            creation_response_d = self.send_request_and_get_response("CreateSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterKind": filter_kind,
                "filterSettings": default_values # 使用 0dB 增益创建
            })
            if creation_response_d and creation_response_d.get('requestStatus', {}).get('result') is True:
                print(f"成功创建 EQ 滤镜 '{filter_name}'")
                self.audio_eq_control["original_gains"] = default_values.copy()
                print(f"原始 EQ 增益已设为默认值: {self.audio_eq_control['original_gains']}")
                return True
            else:
                print(f"错误：创建 EQ 滤镜 '{filter_name}' (类型: {filter_kind}) 失败！(响应: {creation_response_d})")
                return False
            # --- --- #

    def restore_original_audio_gains(self, source_name, filter_name, *args):
        """恢复 EQ 滤镜到原始增益。"""
        original_gains = self.audio_eq_control.get("original_gains")
        if not original_gains:
            print("警告：没有可恢复的原始 EQ 增益。")
            return

        print(f"尝试恢复 EQ 滤镜 '{filter_name}' on '{source_name}' 到原始增益...")
        settings_data = {
            "sourceName": source_name,
            "filterName": filter_name,
            # --- 使用 original_gains (已包含 low, mid, high) --- #
            "filterSettings": original_gains,
            "overlay": False # 恢复时覆盖
        }
        response_d = self.send_request_and_get_response("SetSourceFilterSettings", settings_data, timeout=1)
        if response_d and response_d.get('requestStatus', {}).get('result') is True:
            print("成功恢复原始 EQ 增益。")
        else:
            print("警告：恢复原始 EQ 增益失败或超时。")

    def execute_audio_eq_change(self, *args):
        """执行一次 EQ 增益变更。"""
        if not self.audio_eq_control["enabled"] or not self.is_connected:
            return

        source = self.audio_eq_control["source_name"]
        filter_name = self.audio_eq_control["filter_name"]
        new_settings = {}
        log_parts = []

        # --- 使用 low, mid, high --- #
        for band in ["low", "mid", "high"]:
            min_gain = self.audio_eq_control.get(f"min_{band}_gain")
            max_gain = self.audio_eq_control.get(f"max_{band}_gain")
            new_gain = min_gain
            # --- 确保 Min/Max 有效 --- #
            if max_gain is not None and min_gain is not None and max_gain > min_gain:
                new_gain = random.uniform(min_gain, max_gain)
            elif min_gain is not None:
                new_gain = min_gain
            else:
                new_gain = 0.0 # 默认增益

            # --- 使用 low, mid, high 作为参数名 --- #
            obs_param_name = band
            new_settings[obs_param_name] = new_gain
            log_parts.append(f"{band}={new_gain:.1f}dB")

        print(f"音频 EQ: 为 '{source}'/'{filter_name}' 设置新增益: {', '.join(log_parts)}")

        settings_data = {
            "sourceName": source,
            "filterName": filter_name,
            "filterSettings": new_settings,
            "overlay": True # 合并设置
        }
        self.send_obs_request("SetSourceFilterSettings", settings_data)

    # ... (音频逻辑等)

    # --- 添加缺失的颜色执行函数 --- #
    def execute_color_change(self, *args):
        """执行一次颜色参数变更。"""
        # --- 增加日志和 try-except --- #
        print("定时器触发: 进入 execute_color_change")
        try:
            if not self.color_control["enabled"] or not self.is_connected:
                print("execute_color_change: 功能未启用或未连接，退出。")
                return

            source = self.color_control["source_name"]
            filter_name = self.color_control["filter_name"]
            new_settings = {}
            log_parts = []

            # print("execute_color_change: 正在生成随机参数...") # 减少日志
            for param in ["hue", "saturation", "brightness", "contrast", "gamma"]:
                min_val = self.color_control.get(f"min_{param}")
                max_val = self.color_control.get(f"max_{param}")
                new_val = min_val
                if max_val is not None and min_val is not None and max_val > min_val:
                    new_val = random.uniform(min_val, max_val)
                elif min_val is not None:
                    new_val = min_val
                else:
                    defaults = {"hue": 0, "saturation": 1, "brightness": 0, "contrast": 1, "gamma": 1}
                    new_val = defaults.get(param, 0 if param in ["hue", "brightness"] else 1)

                obs_param_name = param
                if param == "hue": obs_param_name = "hue_shift"
                new_settings[obs_param_name] = new_val
                log_parts.append(f"{param}={new_val:.2f}")

            print(f"颜色: 为 '{source}'/'{filter_name}' 设置新参数: {', '.join(log_parts)}")

            settings_data = {
                "sourceName": source,
                "filterName": filter_name,
                "filterSettings": new_settings,
                "overlay": False
            }
            # print("execute_color_change: 正在发送请求...") # 减少日志
            self.send_obs_request("SetSourceFilterSettings", settings_data)
            print("execute_color_change: 请求已发送。")

        except Exception as e:
            print(f"严重错误：在 execute_color_change 核心逻辑中发生未处理异常: {e}")
            # 发生错误时停止定时器，防止无限出错
            if self.color_control["timer"].isActive():
                self.color_control["timer"].stop()
                print("错误发生，已停止颜色控制定时器。请禁用并重新启用以尝试恢复。")
                # 考虑自动取消勾选？
                # self.color_checkbox.setChecked(False)
    # -------------------------- #

    # ... (音频逻辑等)

    # --- 新增：启用/禁用功能控件的方法 --- #
    def enable_features(self):
        """启用主窗口的核心功能控件。"""
        # print("启用功能...")
        # 遍历所有需要控制的控件并设置为 Enabled
        self.connect_button.setEnabled(True)
        self.tab_widget.setEnabled(True)

        # 强制同步UI控件和内存变量，确保数据一致性
        self.sync_ui_with_memory()
        # 可以在这里更细致地控制每个 Tab 内部的控件，但先启用整个 TabWidget
        # 例如: self.speed_checkbox.setEnabled(True), self.speed_source_combo.setEnabled(True), ...

    def sync_ui_with_memory(self):
        """强制同步UI控件和内存变量，确保数据一致性"""
        print("🔄 正在同步UI控件和内存变量...")

        # 同步智能加减速控制
        print(f"⚡ 同步智能加减速: {self.speed_control['min_speed']}-{self.speed_control['max_speed']}")
        self.speed_min_spinbox.setValue(self.speed_control['min_speed'])
        self.speed_max_spinbox.setValue(self.speed_control['max_speed'])

        # 同步模糊去重控制
        print(f"🌫️ 同步模糊去重: {self.blur_control['min_radius']}-{self.blur_control['max_radius']}, 间隔:{self.blur_control['interval_ms']}ms")
        self.blur_min_radius_spin.setValue(self.blur_control['min_radius'])
        self.blur_max_radius_spin.setValue(self.blur_control['max_radius'])
        self.blur_interval_spin.setValue(self.blur_control['interval_ms'])
        self.blur_filter_name_edit.setText(self.blur_control['filter_name'])

        # 同步移动变换控制
        print(f"🔄 同步移动变换: 缩放{self.transform_control['fixed_scale']}, 间隔:{self.transform_control['interval_secs']}s")
        self.transform_scale_spin.setValue(self.transform_control['fixed_scale'])
        self.transform_interval_spin.setValue(self.transform_control['interval_secs'])
        self.transform_transition_spin.setValue(self.transform_control['transition_secs'])

        # 同步颜色去重控制
        print(f"🎨 同步颜色去重: 间隔{self.color_control['interval_ms']}ms")
        self.color_interval_spin.setValue(self.color_control['interval_ms'])
        self.color_filter_name_edit.setText(self.color_control['filter_name'])
        for param in ["hue", "saturation", "brightness", "contrast", "gamma"]:
            min_spin = getattr(self, f"color_{param}_min_spin")
            max_spin = getattr(self, f"color_{param}_max_spin")
            min_spin.setValue(self.color_control[f'min_{param}'])
            max_spin.setValue(self.color_control[f'max_{param}'])

        # 同步音频EQ控制
        print(f"🎵 同步音频EQ: 间隔{self.audio_eq_control['interval_secs']}s")
        self.audio_interval_spin.setValue(self.audio_eq_control['interval_secs'])
        self.audio_filter_name_edit.setText(self.audio_eq_control['filter_name'])
        for band in ["low", "mid", "high"]:
            min_spin = getattr(self, f"audio_{band}_min_spin")
            max_spin = getattr(self, f"audio_{band}_max_spin")
            min_spin.setValue(self.audio_eq_control[f'min_{band}_gain'])
            max_spin.setValue(self.audio_eq_control[f'max_{band}_gain'])

        # 同步断音控制
        print(f"🔇 同步断音控制: 间隔{self.audio_mute_control['min_interval_secs']}-{self.audio_mute_control['max_interval_secs']}s")
        self.audio_mute_interval_min_spin.setValue(self.audio_mute_control['min_interval_secs'])
        self.audio_mute_interval_max_spin.setValue(self.audio_mute_control['max_interval_secs'])
        self.audio_mute_duration_min_spin.setValue(self.audio_mute_control['min_mute_duration_secs'])
        self.audio_mute_duration_max_spin.setValue(self.audio_mute_control['max_mute_duration_secs'])

        # 同步音频音量控制
        print(f"🎚️ 同步音频音量控制: {self.audio_volume_control['volume_min_percent']}% - {self.audio_volume_control['volume_max_percent']}%")
        self.audio_volume_min_spin.setValue(self.audio_volume_control['volume_min_percent'])
        self.audio_volume_max_spin.setValue(self.audio_volume_control['volume_max_percent'])
        self.audio_volume_interval_min_spin.setValue(self.audio_volume_control['min_interval_secs'])
        self.audio_volume_interval_max_spin.setValue(self.audio_volume_control['max_interval_secs'])

        print("✅ UI控件和内存变量同步完成")

    def disable_features(self):
        """禁用主窗口的核心功能控件。"""
        print("禁用功能...")
        # 遍历所有需要控制的控件并设置为 Disabled
        self.connect_button.setEnabled(False)
        self.tab_widget.setEnabled(False)
        # 确保所有模块的定时器也停止？ (可能在 toggle 函数里已经处理了)
        # 例如: self.speed_checkbox.setEnabled(False), ...

    # --- 修改：检查激活状态并处理 (旧 check_activation) --- #
    def check_and_handle_activation(self):
        """检查激活状态，每次都显示激活对话框，并根据结果启用/禁用功能。"""
        try:
            # 从设置中读取当前激活的卡密
            settings = QSettings('OBSController', 'Settings')
            current_active_key = settings.value('activation/key', '')
            
            # 获取机器码
            machine_code = get_machine_code()
            print(f"当前设备机器码: {machine_code}")
            
            # 每次都显示激活对话框
            dialog = ActivationDialog(self)
            
            # 如果有已激活的卡密，显示在输入框中
            if current_active_key:
                dialog.key_input.setText(current_active_key)
                
            # 如果用户取消了对话框
            if dialog.exec_() != QDialog.Accepted:
                print("用户取消激活对话框")
                if not current_active_key:  # 如果之前没有激活过，禁用功能
                    self.disable_features()
                return False
                
            # 获取用户输入的卡密
            key = dialog.get_key().strip()
            if not key:
                if not current_active_key:  # 如果之前没有激活过，禁用功能
                    self.disable_features()
                return False
            hash_key = hashlib.sha256(key.encode()).hexdigest()

            # 验证卡密
            is_valid, duration_days = self.validate_key(key)
            if is_valid:
                try:
                    headers = {
                        'Authorization': f'token {GITEE_TOKEN}',
                        'Content-Type': 'application/json;charset=UTF-8'
                    }
                    
                    # 获取已生成的卡密列表
                    generated_response = requests.get(
                        f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/{GENERATED_KEYS_FILE}',
                        headers=headers,
                        timeout=10
                    )
                    
                    if generated_response.status_code == 200:
                        # 解析已生成的卡密列表
                        generated_content = base64.b64decode(generated_response.json()['content']).decode('utf-8')
                        generated_keys = json.loads(generated_content)
                        
                        # 查找卡密信息
                        key_info = None
                        key_index = -1
                        for i, info in enumerate(generated_keys):
                            if info.get("hash", "") == hash_key:
                                key_info = info
                                key_index = i
                                break
                                
                        if key_info:
                            # 检查卡密状态
                            if key_info.get("status") == "已激活":
                                # 检查是否是当前设备
                                stored_machine_code = key_info.get("machine_code", "")
                                if stored_machine_code and stored_machine_code != machine_code:
                                    QMessageBox.warning(self, "激活失败", 
                                        "此卡密已在其他设备上激活\n"
                                        "请先解绑后再在此设备上激活\n"
                                        "如有疑问，请联系客服")
                                    if not current_active_key:  # 如果之前没有激活过，禁用功能
                                        self.disable_features()
                                    return False
                                
                                # 检查是否过期
                                expiry_time = datetime.strptime(key_info["expiry_time"], "%Y-%m-%d %H:%M:%S")
                                if expiry_time < datetime.now():
                                    QMessageBox.warning(self, "激活失败", 
                                        "此卡密已过期\n"
                                        "请联系客服续费或购买新卡密")
                                    if not current_active_key:  # 如果之前没有激活过，禁用功能
                                        self.disable_features()
                                    return False
                                    
                                # 如果是同一设备或未绑定设备，更新机器码
                                if not stored_machine_code:
                                    key_info["machine_code"] = machine_code
                                    print(f"更新机器码: {machine_code}")
                                    
                                    # 更新云端数据
                                    updated_content = json.dumps(generated_keys, ensure_ascii=False, indent=2)
                                    updated_content_base64 = base64.b64encode(updated_content.encode('utf-8')).decode('utf-8')
                                    
                                    update_response = requests.put(
                                        f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/{GENERATED_KEYS_FILE}',
                                        headers=headers,
                                        json={
                                            'message': f'更新机器码: {key}',
                                            'content': updated_content_base64,
                                            'sha': generated_response.json()['sha']
                                        },
                                        timeout=10
                                    )
                                    
                                    if update_response.status_code not in [200, 201]:
                                        print(f"警告：更新机器码失败: {update_response.status_code}")
                                    
                                # 已激活且未过期，保存设置
                                settings.setValue('activation/key', key)
                                settings.setValue('activation/expiry', key_info["expiry_time"])
                                self.enable_features()
                                
                                # 显示剩余天数
                                remaining_days = (expiry_time - datetime.now()).days
                                QMessageBox.information(self, "激活状态", 
                                    f"卡密验证成功\n"
                                    f"到期时间：{expiry_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                                    f"剩余天数：{remaining_days}天")
                                return True
                                    
                            # 未激活过，进行新的激活
                            new_expiry_date = datetime.now() + timedelta(days=duration_days)
                            
                            # 更新当前卡密的激活状态
                            key_info["status"] = "已激活"
                            key_info["activation_time"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            key_info["expiry_time"] = new_expiry_date.strftime("%Y-%m-%d %H:%M:%S")
                            key_info["machine_code"] = machine_code  # 绑定机器码
                            print(f"首次激活，绑定机器码: {machine_code}")
                            
                            # 更新云端数据
                            updated_content = json.dumps(generated_keys, ensure_ascii=False, indent=2)
                            updated_content_base64 = base64.b64encode(updated_content.encode('utf-8')).decode('utf-8')
                            
                            update_response = requests.put(
                                f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/{GENERATED_KEYS_FILE}',
                                headers=headers,
                                json={
                                    'message': f'激活卡密: {key}',
                                    'content': updated_content_base64,
                                    'sha': generated_response.json()['sha']
                                },
                                timeout=10
                            )
                            
                            if update_response.status_code in [200, 201]:
                                # 保存激活信息
                                settings.setValue('activation/key', key)
                                settings.setValue('activation/expiry', new_expiry_date.strftime("%Y-%m-%d %H:%M:%S"))
                                
                                # 启用功能
                                self.enable_features()
                                
                                QMessageBox.information(self, "激活成功", 
                                    f"软件已成功激活\n"
                                    f"到期时间：{new_expiry_date.strftime('%Y-%m-%d %H:%M:%S')}")
                                return True
                            else:
                                print(f"更新云端数据失败: {update_response.status_code}")
                                print(f"错误信息: {update_response.text}")
                                QMessageBox.warning(self, "激活失败", 
                                    "更新激活状态失败\n"
                                    "请检查网络连接后重试")
                                if not current_active_key:  # 如果之前没有激活过，禁用功能
                                    self.disable_features()
                                return False
                        else:
                            QMessageBox.warning(self, "激活失败", 
                                "无效的卡密\n"
                                "如有疑问，请联系客服")
                            if not current_active_key:  # 如果之前没有激活过，禁用功能
                                self.disable_features()
                            return False
                    else:
                        QMessageBox.warning(self, "激活失败", 
                            "验证服务器连接失败\n"
                            "请检查网络连接后重试")
                        if not current_active_key:  # 如果之前没有激活过，禁用功能
                            self.disable_features()
                        return False
                        
                except Exception as e:
                    print(f"激活过程出错: {str(e)}")
                    QMessageBox.warning(self, "激活失败", 
                        f"激活过程出现错误：{str(e)}\n"
                        "请稍后重试，如果问题持续存在，请联系客服")
                    if not current_active_key:  # 如果之前没有激活过，禁用功能
                        self.disable_features()
                    return False
            else:
                QMessageBox.warning(self, "激活失败", 
                    "无效的卡密\n"
                    "如有疑问，请联系客服")
                if not current_active_key:  # 如果之前没有激活过，禁用功能
                    self.disable_features()
                return False
                
        except Exception as e:
            print(f"激活过程出现异常: {str(e)}")
            QMessageBox.warning(self, "错误", 
                f"激活过程出现错误：{str(e)}\n"
                "请稍后重试，如果问题持续存在，请联系客服")
            if not current_active_key:  # 如果之前没有激活过，禁用功能
                self.disable_features()
            return False

    def validate_key(self, key):
        """验证卡密是否有效，返回 (bool, int) 元组，表示是否有效和有效期天数"""
        try:
            # 验证卡密格式 (包含8位签名)
            pattern = f"^{KEY_PREFIX}([a-f0-9]{{16}})([0-9]{{4}})([a-f0-9]{{4}})([a-f0-9]{{8}})$"
            match = re.match(pattern, key, re.IGNORECASE)
            if not match:
                print(f"卡密格式不正确: {key}")
                QMessageBox.warning(self, "激活失败", "卡密格式不正确，请检查后重试")
                return False, 0
            
            # 获取各部分
            hex_part = match.group(1)
            days = int(match.group(2))
            check_code = match.group(3)
            signature = match.group(4)
            
            # 验证签名
            data_to_sign = f"{hex_part}{days:04d}{check_code}"
            expected_signature = self.generate_signature(data_to_sign)
            if signature.lower() != expected_signature.lower():
                print(f"签名验证失败: 期望={expected_signature}, 实际={signature}")
                QMessageBox.warning(self, "激活失败", "卡密签名无效，可能是伪造的卡密")
                return False, 0
            
            # 验证校验码
            expected_check_code = self.generate_check_code(hex_part, days)
            if check_code.lower() != expected_check_code.lower():
                print(f"校验码不正确: 期望={expected_check_code}, 实际={check_code}")
                QMessageBox.warning(self, "激活失败", "卡密校验失败，可能是无效或被篡改的卡密")
                return False, 0
            
            # 验证天数
            if days <= 0:
                print("卡密天数无效")
                QMessageBox.warning(self, "激活失败", "卡密天数无效")
                return False, 0
            
            # 检查Gitee Token是否有效
            if not GITEE_TOKEN or len(GITEE_TOKEN) < 20:
                print("Gitee Token未配置或无效")
                QMessageBox.warning(self, "激活失败", 
                    "激活服务器配置错误\n"
                    "请联系客服处理")
                return False, 0
            
            # 从云端获取卡密信息
            try:
                headers = {
                    'Authorization': f'token {GITEE_TOKEN}',
                    'Content-Type': 'application/json;charset=UTF-8'
                }
                
                # 获取作废的卡密列表
                revoked_response = requests.get(
                    f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/revoked_keys.json',
                    headers=headers,
                    timeout=10
                )
                
                # 获取已生成的卡密列表
                generated_response = requests.get(
                    f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/{GENERATED_KEYS_FILE}',
                    headers=headers,
                    timeout=10
                )
                
                if revoked_response.status_code == 200 and generated_response.status_code == 200:
                    # 解析作废的卡密列表
                    revoked_content = base64.b64decode(revoked_response.json()['content']).decode('utf-8')
                    revoked_keys = json.loads(revoked_content)
                    
                    # 检查是否在作废列表中
                    if key in revoked_keys:
                        print("此卡密已被作废")
                        QMessageBox.warning(self, "激活失败", 
                            "此卡密已被作废，无法继续使用\n"
                            "如有疑问，请联系客服处理")
                        return False, 0
                    
                    # 解析已生成的卡密列表
                    generated_content = base64.b64decode(generated_response.json()['content']).decode('utf-8')
                    generated_keys = json.loads(generated_content)
                    
                    # 检查是否在生成列表中
                    hash_key = hashlib.sha256(key.encode()).hexdigest()
                    key_exists = False
                    for key_info in generated_keys:
                        if key_info.get("hash", "") == hash_key:
                            key_exists = True
                            break
                    if not key_exists:
                        print(f"此卡密未在生成列表中或已被删除: {key}")
                        QMessageBox.warning(self, "激活失败", 
                            "无效的卡密\n"
                            "可能的原因：\n"
                            "1. 此卡密已被删除\n"
                            "2. 此卡密不是正版卡密\n"
                            "如有疑问，请联系客服处理")
                        return False, 0
                        
                elif revoked_response.status_code == 401 or generated_response.status_code == 401:
                    print("Gitee Token无效或过期")
                    QMessageBox.warning(self, "激活失败", 
                        "激活服务器认证失败\n"
                        "请联系客服处理")
                    return False, 0
                else:
                    print(f"获取云端数据失败: {revoked_response.status_code}, {generated_response.status_code}")
                    QMessageBox.warning(self, "激活失败", 
                        "无法连接到激活服务器\n"
                        "请检查网络连接后重试\n"
                        "如果网络正常但仍无法激活，请联系客服")
                    return False, 0
                    
            except requests.exceptions.Timeout:
                print("连接超时")
                QMessageBox.warning(self, "激活失败", 
                    "连接激活服务器超时\n"
                    "请检查网络连接后重试")
                return False, 0
                
            except requests.exceptions.ConnectionError:
                print("网络连接错误")
                QMessageBox.warning(self, "激活失败", 
                    "无法连接到网络\n"
                    "请确保电脑已连接到互联网后重试")
                return False, 0
                
            except Exception as e:
                print(f"检查云端数据时出错: {str(e)}")
                QMessageBox.warning(self, "激活失败", 
                    "验证卡密时发生错误\n"
                    "请检查网络连接后重试\n"
                    "如果问题持续存在，请联系客服")
                return False, 0
            
            # 所有检查都通过，返回有效和天数
            # print(f"卡密验证成功: {key}, 天数: {days}")
            return True, days
            
        except Exception as e:
            print(f"验证过程出现异常: {str(e)}")
            QMessageBox.warning(self, "激活失败", 
                "验证过程出现错误\n"
                "请稍后重试，如果问题持续存在，请联系客服")
            return False, 0

    def generate_check_code(self, hex_part, days):
        """生成校验码
        @param hex_part: 16位十六进制部分
        @param days: 天数
        @return: 4位十六进制校验码
        """
        # 将天数转换为4位十进制字符串（补零）
        days_str = f"{days:04d}"
        
        # 组合原始数据
        data = f"{hex_part}{days_str}"
        
        # 使用简单的异或运算生成校验码
        check = 0
        for i in range(0, len(data), 4):
            chunk = data[i:i+4]
            # 补齐不足4位的部分
            chunk = chunk.ljust(4, '0')
            # 将十六进制chunk转换为整数并异或
            check ^= int(chunk, 16)
        
        # 返回4位校验码
        return format(check & 0xFFFF, '04x')

    def generate_signature(self, data):
        """使用HMAC-SHA256生成签名
        @param data: 要签名的数据
        @return: 8位十六进制签名
        """
        hmac_obj = hmac.new(
            SIGNATURE_KEY.encode('utf-8'),
            data.encode('utf-8'),
            hashlib.sha256
        )
        # 取HMAC的前4个字节（8位十六进制）
        return hmac_obj.hexdigest()[:8]

    # --- 新增：确认恢复默认设置 ---
    def confirm_restore_defaults(self, *args):
        """确认对话框，防止误操作"""
        reply = QMessageBox.question(self, 
                                    "确认操作", 
                                    "您确定要恢复所有设置为默认值吗？\n此操作无法撤销。",
                                    QMessageBox.Yes | QMessageBox.No,
                                    QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            self.restore_default_settings()
    
    # --- 新增：恢复默认设置 ---
    def restore_default_settings(self):
        """将所有控件恢复为默认值"""
        print("正在恢复默认设置...")

        # 保存激活码相关信息
        activation_key = self.settings.value("activation/key", "")
        activation_expiry = self.settings.value("activation/expiry_date", "")
        last_key = self.settings.value("activation/last_key", "")
        
        # 清除QSettings中的设置数据
        self.settings.clear()
        self.settings.sync()
        
        # 恢复激活码相关信息
        if activation_key:
            self.settings.setValue("activation/key", activation_key)
        if activation_expiry:
            self.settings.setValue("activation/expiry_date", activation_expiry)
        if last_key:
            self.settings.setValue("activation/last_key", last_key)
        self.settings.sync()

        # --- 恢复速度控制 ---
        self.speed_checkbox.setChecked(False)
        self.speed_min_spinbox.setValue(90)
        self.speed_max_spinbox.setValue(120)
        self.speed_control.update({
            'enabled': False,
            'min_speed': 90,
            'max_speed': 120,
        })
        
        # --- 恢复模糊控制 ---
        self.blur_checkbox.setChecked(False)
        self.blur_filter_name_edit.setText("Composite Blur")
        self.blur_min_radius_spin.setValue(0.0)
        self.blur_max_radius_spin.setValue(1.5)
        self.blur_interval_spin.setValue(1000)
        self.blur_control.update({
            'enabled': False,
            'filter_name': "Composite Blur",
            'min_radius': 0.0,
            'max_radius': 1.5,
            'interval_ms': 1000
        })
        
        # --- 恢复移动控制 ---
        self.transform_checkbox.setChecked(False)
        self.transform_scale_spin.setValue(110)
        self.transform_interval_spin.setValue(2.0)
        self.transform_transition_spin.setValue(10.0)
        self.transform_control.update({
            'enabled': False,
            'fixed_scale': 110,
            'interval_secs': 2,
            'transition_secs': 10,
            'safe_move_x': 0,
            'safe_move_y': 0,
            'base_pos_x': 0,
            'base_pos_y': 0,
            'current_pos_x': 0,
            'current_pos_y': 0,
            'target_pos_x': 0,
            'target_pos_y': 0,
            'is_animating': False,
            'animation_start_time': 0.0,
            'canvas_width': 1080,
            'canvas_height': 1920
        })
        
        # --- 恢复颜色控制 ---
        self.color_checkbox.setChecked(False)
        self.color_filter_name_edit.setText("自动颜色校正")
        # 恢复所有颜色参数
        color_params = {
            'hue': (-2.0, 10.0),
            'saturation': (-0.2, 0.2),
            'brightness': (-0.05, 0.05),
            'contrast': (-0.2, 0.2),
            'gamma': (-0.3, 0.3)
        }
        
        for param, (min_val, max_val) in color_params.items():
            min_spin = getattr(self, f"color_{param}_min_spin")
            max_spin = getattr(self, f"color_{param}_max_spin")
            min_spin.setValue(min_val)
            max_spin.setValue(max_val)
            self.color_control[f'min_{param}'] = min_val
            self.color_control[f'max_{param}'] = max_val
            
        self.color_interval_spin.setValue(1500)
        self.color_control['interval_ms'] = 1500
        self.color_control['enabled'] = False
        
        # --- 恢复音频EQ控制 ---
        self.audio_checkbox.setChecked(False)
        self.audio_filter_name_edit.setText("3段式均衡器")
        
        # 恢复所有音频参数
        audio_bands = {
            'low': (-2.0, 2.0),
            'mid': (-2.0, 2.0),
            'high': (-2.0, 2.0)
        }
        
        for band, (min_val, max_val) in audio_bands.items():
            min_spin = getattr(self, f"audio_{band}_min_spin")
            max_spin = getattr(self, f"audio_{band}_max_spin")
            min_spin.setValue(min_val)
            max_spin.setValue(max_val)
            self.audio_eq_control[f'min_{band}_gain'] = min_val
            self.audio_eq_control[f'max_{band}_gain'] = max_val
            
        self.audio_interval_spin.setValue(1.0)
        self.audio_eq_control['interval_secs'] = 1.0
        self.audio_eq_control['enabled'] = False

        # --- 恢复断音控制 ---
        self.audio_mute_checkbox.setChecked(False)
        self.audio_mute_interval_min_spin.setValue(30.0)
        self.audio_mute_interval_max_spin.setValue(40.0)
        self.audio_mute_duration_min_spin.setValue(0.1)
        self.audio_mute_duration_max_spin.setValue(0.5)
        self.audio_mute_control.update({
            'enabled': False,
            'min_interval_secs': 30.0,
            'max_interval_secs': 40.0,
            'min_mute_duration_secs': 0.1,
            'max_mute_duration_secs': 0.5,
            'is_muted': False
        })

        # --- 恢复压缩器控制 ---
        self.compressor_enable_checkbox.setChecked(False)
        self.compressor_filter_name_edit.setText("自动压缩控制")

        # 恢复压缩器参数
        compressor_params = {
            'ratio': (10.0, 13.0),
            'threshold': (-20.0, -18.0),
            'output_gain': (0.0, 1.0),
            'release': (50, 150)
        }

        for param, (min_val, max_val) in compressor_params.items():
            min_spin = getattr(self, f"compressor_{param}_min_spin")
            max_spin = getattr(self, f"compressor_{param}_max_spin")
            min_spin.setValue(min_val)
            max_spin.setValue(max_val)
            self.compressor_control[f'min_{param}'] = min_val
            self.compressor_control[f'max_{param}'] = max_val

        self.compressor_interval_spin.setValue(1.0)
        self.compressor_control.update({
            'enabled': False,
            'interval_secs': 1.0
        })

        # --- 恢复增益控制 ---
        self.gain_enable_checkbox.setChecked(False)
        self.gain_filter_name_edit.setText("自动增益控制")
        self.gain_gain_min_spin.setValue(-3.0)  # 新默认值：-3dB
        self.gain_gain_max_spin.setValue(3.0)   # 新默认值：+3dB
        self.gain_interval_spin.setValue(1.0)
        self.gain_control.update({
            'enabled': False,
            'min_gain': -3.0,
            'max_gain': 3.0,
            'interval_secs': 1.0
        })

        # --- 恢复音量控制 ---
        self.audio_volume_checkbox.setChecked(False)
        self.audio_volume_min_spin.setValue(60)  # 对应0.6倍数
        self.audio_volume_max_spin.setValue(80)  # 对应0.8倍数
        self.audio_volume_interval_min_spin.setValue(1.0)
        self.audio_volume_interval_max_spin.setValue(5.0)
        self.audio_volume_control.update({
            'enabled': False,
            'volume_min_percent': 60,
            'volume_max_percent': 80,
            'min_interval_secs': 1.0,
            'max_interval_secs': 5.0,
            'is_active': False
        })

        # --- 恢复插件去重控制 ---
        self.plugin_dedup_checkbox.setChecked(False)
        self.plugin_dedup_control['enabled'] = False

        # 恢复每个插件的默认设置
        default_settings = [
            {"min_interval": 10.0, "max_interval": 30.0, "min_duration": 5.0, "max_duration": 15.0},
            {"min_interval": 15.0, "max_interval": 40.0, "min_duration": 8.0, "max_duration": 20.0},
            {"min_interval": 12.0, "max_interval": 35.0, "min_duration": 6.0, "max_duration": 18.0}
        ]

        for i, plugin in enumerate(self.plugin_dedup_control["plugins"]):
            if i < len(default_settings):
                defaults = default_settings[i]
                plugin.update({
                    'min_interval_secs': defaults["min_interval"],
                    'max_interval_secs': defaults["max_interval"],
                    'min_duration_secs': defaults["min_duration"],
                    'max_duration_secs': defaults["max_duration"],
                    'is_active': False
                })

                # 更新UI控件
                if i < len(self.plugin_controls):
                    controls = self.plugin_controls[i]
                    controls['interval_min'].setValue(defaults["min_interval"])
                    controls['interval_max'].setValue(defaults["max_interval"])
                    controls['duration_min'].setValue(defaults["min_duration"])
                    controls['duration_max'].setValue(defaults["max_duration"])

        # 提示用户已恢复默认设置
        QMessageBox.information(self,
                               "操作成功",
                               "所有设置已恢复为默认值。",
                               QMessageBox.Ok)
        
        print("默认设置已恢复。")
    # ------------------ #
    
    # --- 新增：加载设置 --- #

    def reset_to_center(self):
        """重置视频源到画布中心（以当前缩放为基础再放大，并计算安全区）"""
        if not self.is_connected:
            return
        
        state = self.transform_control
        source_name = state["source_name"]
        if not source_name:
            return
        
        # 获取场景和项目ID
        scene_name = self.get_current_scene_name()
        item_id = self.get_scene_item_id(source_name)
        if not scene_name or not item_id:
            print("错误：无法获取场景或项目ID")
            return
        
        # 获取当前 transform，保持当前 scale
        transform = self.get_original_transform(scene_name, item_id)
        if not transform:
            print("错误：无法获取当前 transform")
            return
        base_scale_x = transform.get("scaleX", 1.0)
        base_scale_y = transform.get("scaleY", 1.0)
        user_scale = state["fixed_scale"] / 100.0  # 用户设置的放大倍数
        scale_x = base_scale_x * user_scale
        scale_y = base_scale_y * user_scale
        canvas_w = self.transform_control["canvas_width"]
        canvas_h = self.transform_control["canvas_height"]
        # 获取素材原始尺寸（优先 transform，兼容性处理）
        source_w = transform.get("sourceWidth", canvas_w)
        source_h = transform.get("sourceHeight", canvas_h)
        scaled_w = source_w * scale_x
        scaled_h = source_h * scale_y
        # 居中坐标
        pos_x = (canvas_w - scaled_w) / 2
        pos_y = (canvas_h - scaled_h) / 2
        # 计算安全移动范围
        safe_move_x = max(0, (scaled_w - canvas_w) / 2)
        safe_move_y = max(0, (scaled_h - canvas_h) / 2)
        state["safe_move_x"] = int(safe_move_x)
        state["safe_move_y"] = int(safe_move_y)
        if state["debug"]:
            print(f"放大后素材尺寸: {scaled_w:.0f}x{scaled_h:.0f}, 居中坐标: ({pos_x:.0f}, {pos_y:.0f}), 安全区: X±{safe_move_x:.0f}, Y±{safe_move_y:.0f}")
        # 应用变换（放大并居中）
        self.send_obs_request("SetSceneItemTransform", {
            "sceneName": scene_name,
            "sceneItemId": item_id,
            "sceneItemTransform": {
                "positionX": pos_x,
                "positionY": pos_y,
                "scaleX": scale_x,
                "scaleY": scale_y
            }
        })
        # 更新动画状态
        state["current_pos_x"] = pos_x
        state["current_pos_y"] = pos_y
        state["target_pos_x"] = pos_x
        state["target_pos_y"] = pos_y

    def calculate_safe_range(self):
        """计算安全移动范围"""
        state = self.transform_control
        scale = state["fixed_scale"] / 100
        scaled_width = state["canvas_width"] * scale
        scaled_height = state["canvas_height"] * scale
        
        # 安全位移计算（保留15%边距）
        state["safe_move_x"] = int((scaled_width - state["canvas_width"]) / 2 * 0.85)
        state["safe_move_y"] = int((scaled_height - state["canvas_height"]) / 2 * 0.85)
        
        # 限制最大位移
        state["safe_move_x"] = max(0, min(state["safe_move_x"], 300))
        state["safe_move_y"] = max(0, min(state["safe_move_y"], 300))
        
        if state["debug"]:
            print(f"安全范围: X±{state['safe_move_x']}px, Y±{state['safe_move_y']}px | 缩放: {scale:.1f}x")

    def generate_new_target(self):
        """生成随机位移目标"""
        state = self.transform_control
        dx = random.randint(-state["safe_move_x"], state["safe_move_x"])
        dy = random.randint(-state["safe_move_y"], state["safe_move_y"])
        
        # 基于当前居中位置计算
        scale = state["fixed_scale"] / 100
        state["target_pos_x"] = (state["canvas_width"] - state["canvas_width"] * scale) / 2 + dx
        state["target_pos_y"] = (state["canvas_height"] - state["canvas_height"] * scale) / 2 + dy
        
        if state["debug"]:
            print(f"新位移: dx={dx}, dy={dy} | 目标位置: ({state['target_pos_x']:.0f}, {state['target_pos_y']:.0f})")

    def update_animation(self, current_time):
        """平滑过渡动画"""
        state = self.transform_control
        elapsed = current_time - state["animation_start_time"]
        ratio = min(elapsed / state["transition_secs"], 1.0)
        
        # 二次缓动曲线
        ease_ratio = ratio * ratio * (3 - 2 * ratio)
        
        # 计算插值
        new_x = state["current_pos_x"] + (state["target_pos_x"] - state["current_pos_x"]) * ease_ratio
        new_y = state["current_pos_y"] + (state["target_pos_y"] - state["current_pos_y"]) * ease_ratio
        
        # 获取场景和项目ID
        scene_name = self.get_current_scene_name()
        item_id = self.get_scene_item_id(state["source_name"])
        if not scene_name or not item_id:
            print("错误：无法获取场景或项目ID")
            return
            
        # 应用变换
        self.send_obs_request("SetSceneItemTransform", {
            "sceneName": scene_name,
            "sceneItemId": item_id,
            "sceneItemTransform": {
                "positionX": new_x,
                "positionY": new_y
            }
        })
        
        # 动画完成
        if ratio >= 1.0:
            state["current_pos_x"] = state["target_pos_x"]
            state["current_pos_y"] = state["target_pos_y"]
            state["is_animating"] = False

    def check_transform_animation_status(self, *args):
        """定时器回调：检查动画状态并在需要时启动新动画"""
        if not self.transform_control["enabled"] or not self.is_connected:
            return
            
        state = self.transform_control
        current_time = time.time()
        
        # 初始状态检查
        if state["current_pos_x"] == 0 and state["current_pos_y"] == 0:
            self.reset_to_center()
            return
            
        # 定时触发新位移
        if not state["is_animating"] and (current_time - state["animation_start_time"]) > state["interval_secs"]:
            self.calculate_safe_range()
            self.generate_new_target()
            state["animation_start_time"] = current_time
            state["is_animating"] = True
            
        # 更新动画
        if state["is_animating"]:
            self.update_animation(current_time)

    def get_current_scene_name(self):
        """获取当前场景名称"""
        response = self.send_request_and_get_response("GetCurrentProgramScene")
        if response and response.get('requestStatus', {}).get('result') is True:
            return response.get('responseData', {}).get('currentProgramSceneName')
        return None

    def get_scene_item_id(self, source_name):
        """获取场景项ID"""
        scene_name = self.get_current_scene_name()
        if not scene_name:
            return None
            
        response = self.send_request_and_get_response("GetSceneItemId", {
            "sceneName": scene_name,
            "sourceName": source_name
        })
        if response and response.get('requestStatus', {}).get('result') is True:
            return response.get('responseData', {}).get('sceneItemId')
        return None

    def security_check(self):
        """定期安全检查"""
        if not self.anti_debug.perform_security_check():
            self.close()
            sys.exit(1)

    def check_for_updates(self):
        """检查更新"""
        try:
            # 从Gitee获取版本信息
            headers = {
                'Authorization': f'token {GITEE_TOKEN}',
                'Content-Type': 'application/json;charset=UTF-8'
            }
            
            response = requests.get(
                f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/version.json',
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                # 获取文件内容
                file_content = response.json()
                if isinstance(file_content, dict) and 'content' in file_content:
                    content = base64.b64decode(file_content['content']).decode('utf-8')
                    version_info = json.loads(content)
                    
                    # 检查版本号
                    if version_info['version'] > VERSION:
                        # 检查是否需要强制更新
                        if version_info.get('force_update', False):
                            msg = "发现新版本，此更新为强制更新，请立即更新！\n\n"
                        else:
                            msg = "发现新版本，是否更新？\n\n"
                            
                        msg += f"当前版本：{VERSION}\n"
                        msg += f"最新版本：{version_info['version']}\n"
                        msg += f"更新内容：\n{version_info['changelog']}"
                        
                        reply = QMessageBox.question(
                            self, "发现新版本", msg,
                            QMessageBox.Yes | QMessageBox.No,
                            QMessageBox.Yes if version_info.get('force_update', False) else QMessageBox.No
                        )
                        
                        if reply == QMessageBox.Yes:
                            self.download_update(version_info)
                        elif version_info.get('force_update', False):
                            # 如果是强制更新但用户选择不更新，则退出程序
                            sys.exit(0)
                else:
                    print("无效的版本信息响应格式")
                            
        except Exception as e:
            print(f"检查更新失败: {str(e)}")

    def download_update(self, version_info):
        """下载并安装更新"""
        import webbrowser
        try:
            download_url = version_info.get('download_url', '')
            changelog = version_info.get('changelog', '')
            # 判断是否为直链（简单判断 .exe 结尾）
            if download_url.lower().endswith('.exe'):
                # 创建进度对话框
                progress = QProgressDialog("正在下载更新...", "取消", 0, 100, self)
                progress.setWindowModality(Qt.WindowModal)
                progress.setAutoClose(True)
                progress.setAutoReset(True)
                # 获取当前程序路径
                if getattr(sys, 'frozen', False):
                    current_exe = sys.executable
                else:
                    current_exe = __file__
                # 创建更新线程
                self.update_thread = UpdaterThread(
                    download_url,
                    current_exe + '.new'
                )
                # 连接信号
                self.update_thread.progress_signal.connect(progress.setValue)
                self.update_thread.finished_signal.connect(
                    lambda success, msg: self.handle_update_finished(success, msg, current_exe)
                )
                # 启动下载
                self.update_thread.start()
            else:
                # 外链，直接用浏览器打开
                webbrowser.open(download_url)
                # 从 changelog 里提取提取码
                import re
                m = re.search(r'提取码[:：]?\s*([a-zA-Z0-9]{4,})', changelog)
                code = m.group(1) if m else ''
                msg = "已为您打开下载页面，请手动下载新版本。"
                if code:
                    msg += f"\n\n下载提取码：{code}"
                else:
                    msg += "\n\n如有提取码请查看更新内容说明。"
                QMessageBox.information(self, "手动下载", msg)
        except Exception as e:
            QMessageBox.warning(self, "更新失败", f"打开下载页面时出错：{str(e)}")

    def handle_update_finished(self, success, message, current_exe):
        print(f"升级线程完成，success={success}, message={message}")
        if success:
            exe_dir = os.path.dirname(current_exe)
            exe_name = os.path.basename(current_exe)
            # 创建静默升级批处理文件（无echo、无pause）
            batch_content = f'''@echo off
setlocal
set TASKKILL=%SystemRoot%\\System32\\taskkill.exe
cd /d "{exe_dir}"
:wait
%TASKKILL% /F /IM "{exe_name}"
if errorlevel 1 goto wait

:movefile
move /Y "{exe_name}.new" "{exe_name}"
if errorlevel 1 goto movefile

:checkfile
if not exist "{exe_name}" goto checkfile
ping 127.0.0.1 -n 1 >nul

del "%~f0"
endlocal
'''
            batch_path = os.path.join(exe_dir, "update.bat")
            with open(batch_path, 'w') as f:
                f.write(batch_content)
            print(f"已生成升级批处理: {batch_path}")
            # 静默运行批处理（无窗口）
            subprocess.Popen(
                [batch_path],
                shell=True,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
            print("即将退出主程序，等待批处理完成升级...")
            sys.exit(0)
        else:
            QMessageBox.warning(self, "更新失败", message)
            print(f"更新失败: {message}")
            # 清理临时文件
            if os.path.exists(current_exe + '.new'):
                try:
                    os.remove(current_exe + '.new')
                except:
                    pass

    def check_integrity(self):
        """定期完整性检查"""
        if not self.integrity_checker.perform_integrity_check():
            self.close()
            sys.exit(1)

    def toggle_compressor_control(self, state):
        self.compressor_control["enabled"] = state

        if state:
            # 使用统一的音频媒体源
            source = self.audio_source_combo.currentData()
            if source:
                self.compressor_control["source_name"] = source
            else:
                source = self.compressor_control["source_name"]

            if not source:
                print("错误：请先选择音频媒体源")
                self.compressor_enable_checkbox.setChecked(False)
                return

            self.compressor_control["timer"].timeout.connect(self.execute_compressor_change)
            self.compressor_control["timer"].start(int(self.compressor_control["interval_secs"] * 1000))
            self.ensure_compressor_filter_exists_and_save_original(source, self.compressor_control["filter_name"])
        else:
            self.compressor_control["timer"].stop()
            source = self.compressor_control["source_name"]
            if source:
                self.remove_compressor_filter(source, self.compressor_control["filter_name"])
    def update_compressor_setting(self, key, value):
        self.compressor_control[key] = value
        # 立即保存设置到磁盘
        self.save_settings()
        if key == "interval_secs" and self.compressor_control["enabled"]:
            self.compressor_control["timer"].setInterval(int(value * 1000))
    def ensure_compressor_filter_exists_and_save_original(self, source_name, filter_name):
        # 检查并添加压缩滤镜
        if not source_name:
            print("错误：未选择音频源")
            return
            
        print(f"正在检查压缩滤镜: {source_name} -> {filter_name}")
        resp = self.send_request_and_get_response("GetSourceFilter", {
            "sourceName": source_name, 
            "filterName": filter_name
        })
        print(f"获取滤镜响应: {resp}")
        
        if resp and resp.get("requestStatus", {}).get("result") is True:
            print(f"压缩滤镜已存在: {filter_name}")
            return True
        else:
            print(f"压缩滤镜不存在，尝试创建: {filter_name}")
            settings = {
                "ratio": self.compressor_control["min_ratio"],
                "threshold": self.compressor_control["min_threshold"],
                "output_gain": self.compressor_control["min_output_gain"],
                "release": self.compressor_control["min_release"]
            }
            add_resp = self.send_request_and_get_response("CreateSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterKind": "compressor_filter",
                "filterSettings": settings
            })
            print(f"创建滤镜响应: {add_resp}")
            if add_resp and add_resp.get("requestStatus", {}).get("result") is True:
                print(f"成功创建压缩滤镜: {filter_name}")
                return True
            else:
                print(f"创建压缩滤镜失败: {add_resp}")
                return False
    def remove_compressor_filter(self, source_name, filter_name):
        self.send_obs_request("RemoveFilterFromSource", {
            "sourceName": source_name,
            "filterName": filter_name
        })
    def execute_compressor_change(self, *args):
        import random
        source_name = self.compressor_control["source_name"]
        filter_name = self.compressor_control["filter_name"]
        ratio = random.uniform(self.compressor_control["min_ratio"], self.compressor_control["max_ratio"])
        threshold = random.uniform(self.compressor_control["min_threshold"], self.compressor_control["max_threshold"])
        output_gain = random.uniform(self.compressor_control["min_output_gain"], self.compressor_control["max_output_gain"])
        release = random.randint(self.compressor_control["min_release"], self.compressor_control["max_release"])
        
        # 尝试不同的参数名称组合
        settings = {
            "ratio": ratio,
            "threshold": threshold,
            "output_gain": output_gain,
            "release": release,
            "release_time": release,  # 可能的替代名称
            "release_ms": release,    # 可能的替代名称
            "releaseTime": release,   # 可能的替代名称
            "releaseTimeMs": release  # 可能的替代名称
        }
        
        print(f"正在更新压缩器参数: ratio={ratio:.1f}, threshold={threshold:.1f}, output_gain={output_gain:.1f}, release={release}")
        
        resp = self.send_request_and_get_response("SetSourceFilterSettings", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterSettings": settings,
            "overlay": True
        })
        
        print(f"压缩器参数更新响应: {resp}")
        
        # 如果失败，尝试只设置基本参数
        if not resp or resp.get("requestStatus", {}).get("result") is not True:
            print("尝试使用基本参数更新压缩器...")
            basic_settings = {
                "ratio": ratio,
                "threshold": threshold,
                "output_gain": output_gain
            }
            self.send_obs_request("SetSourceFilterSettings", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterSettings": basic_settings,
                "overlay": True
            })
    def toggle_gain_control(self, state):
        self.gain_control["enabled"] = state

        if state:
            # 使用统一的音频媒体源
            source = self.audio_source_combo.currentData()
            if source:
                self.gain_control["source_name"] = source
            else:
                source = self.gain_control["source_name"]

            if not source:
                print("错误：请先选择音频媒体源")
                self.gain_enable_checkbox.setChecked(False)
                return

            self.gain_control["timer"].timeout.connect(self.execute_gain_change)
            self.gain_control["timer"].start(int(self.gain_control["interval_secs"] * 1000))
            self.ensure_gain_filter_exists_and_save_original(source, self.gain_control["filter_name"])
        else:
            self.gain_control["timer"].stop()
            source = self.gain_control["source_name"]
            if source:
                self.remove_gain_filter(source, self.gain_control["filter_name"])
    def update_gain_setting(self, key, value):
        self.gain_control[key] = value
        # 立即保存设置到磁盘
        self.save_settings()
        if key == "interval_secs" and self.gain_control["enabled"]:
            self.gain_control["timer"].setInterval(int(value * 1000))
    def ensure_gain_filter_exists_and_save_original(self, source_name, filter_name):
        # 检查并添加增益滤镜
        if not source_name:
            print("错误：未选择音频源")
            return
            
        print(f"正在检查增益滤镜: {source_name} -> {filter_name}")
        resp = self.send_request_and_get_response("GetSourceFilter", {
            "sourceName": source_name, 
            "filterName": filter_name
        })
        print(f"获取滤镜响应: {resp}")
        
        if resp and resp.get("requestStatus", {}).get("result") is True:
            print(f"增益滤镜已存在: {filter_name}")
            return True
        else:
            print(f"增益滤镜不存在，尝试创建: {filter_name}")
            settings = {"db": self.gain_control["min_gain"]}
            add_resp = self.send_request_and_get_response("CreateSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterKind": "gain_filter",
                "filterSettings": settings
            })
            print(f"创建滤镜响应: {add_resp}")
            if add_resp and add_resp.get("requestStatus", {}).get("result") is True:
                print(f"成功创建增益滤镜: {filter_name}")
                return True
            else:
                print(f"创建增益滤镜失败: {add_resp}")
                return False
    def remove_gain_filter(self, source_name, filter_name):
        self.send_obs_request("RemoveFilterFromSource", {
            "sourceName": source_name,
            "filterName": filter_name
        })
    def execute_gain_change(self, *args):
        import random
        source_name = self.gain_control["source_name"]
        filter_name = self.gain_control["filter_name"]
        gain = random.uniform(self.gain_control["min_gain"], self.gain_control["max_gain"])
        settings = {"db": gain}
        self.send_obs_request("SetSourceFilterSettings", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterSettings": settings,
            "overlay": True
        })

    # --- 断音控制功能 ---
    def update_audio_mute_setting(self, key, value):
        """更新断音控制设置"""
        print(f"更新断音设置: {key} = {value}")
        self.audio_mute_control[key] = value
        # 立即保存设置到磁盘
        self.save_settings()

        # 如果断音控制正在运行，显示当前所有参数
        if self.audio_mute_control["enabled"]:
            print(f"当前断音参数 - 间隔: {self.audio_mute_control['min_interval_secs']:.1f}-{self.audio_mute_control['max_interval_secs']:.1f}秒, 持续: {self.audio_mute_control['min_mute_duration_secs']:.1f}-{self.audio_mute_control['max_mute_duration_secs']:.1f}秒")

        # 验证范围设置
        if key.startswith("min_interval_secs"):
            max_key = "max_interval_secs"
            param = "断音间隔"
            if value > self.audio_mute_control.get(max_key, value):
                print(f"警告：{param} 的最小值 ({value:.1f}) 不能大于最大值 ({self.audio_mute_control.get(max_key):.1f})。自动调整最大值。")
                self.audio_mute_control[max_key] = value
        elif key.startswith("max_interval_secs"):
            min_key = "min_interval_secs"
            param = "断音间隔"
            if value < self.audio_mute_control.get(min_key, value):
                print(f"警告：{param} 的最大值 ({value:.1f}) 不能小于最小值 ({self.audio_mute_control.get(min_key):.1f})。自动调整最小值。")
                self.audio_mute_control[min_key] = value
        elif key.startswith("min_mute_duration_secs"):
            max_key = "max_mute_duration_secs"
            param = "断音持续时间"
            if value > self.audio_mute_control.get(max_key, value):
                print(f"警告：{param} 的最小值 ({value:.1f}) 不能大于最大值 ({self.audio_mute_control.get(max_key):.1f})。自动调整最大值。")
                self.audio_mute_control[max_key] = value
        elif key.startswith("max_mute_duration_secs"):
            min_key = "min_mute_duration_secs"
            param = "断音持续时间"
            if value < self.audio_mute_control.get(min_key, value):
                print(f"警告：{param} 的最大值 ({value:.1f}) 不能小于最小值 ({self.audio_mute_control.get(min_key):.1f})。自动调整最小值。")
                self.audio_mute_control[min_key] = value

    # --- 插件去重控制功能 ---
    def update_plugin_setting(self, plugin_index, key, value):
        """更新指定插件的设置"""
        if 0 <= plugin_index < len(self.plugin_dedup_control["plugins"]):
            self.plugin_dedup_control["plugins"][plugin_index][key] = value
            plugin_name = self.plugin_dedup_control["plugins"][plugin_index]["display_name"]
            print(f"更新插件 {plugin_name} 设置: {key} = {value}")
            # 立即保存设置到磁盘
            self.save_settings()
        else:
            print(f"错误：插件索引 {plugin_index} 超出范围")

    def toggle_plugin_dedup_control(self, state):
        """切换插件去重控制状态"""
        is_enabled = state == Qt.Checked
        print(f"插件去重控制状态切换: {is_enabled}")

        self.plugin_dedup_control["enabled"] = is_enabled

        if is_enabled:
            # 检查连接状态
            if not self.is_connected:
                print("错误：未连接到 OBS，无法启用插件去重")
                self.plugin_dedup_checkbox.setChecked(False)
                return

            # 使用统一的音频媒体源
            source = self.audio_source_combo.currentData()
            if source:
                self.plugin_dedup_control["source_name"] = source
            else:
                source = self.plugin_dedup_control["source_name"]

            if not source:
                print("错误：请先选择音频媒体源")
                self.plugin_dedup_checkbox.setChecked(False)
                return

            print(f"启用插件去重控制，媒体源: {source}")

            # 创建所有插件滤镜
            if not self.create_all_plugin_filters(source):
                print("错误：创建插件滤镜失败")
                self.plugin_dedup_checkbox.setChecked(False)
                return

            # 刷新插件状态
            self.refresh_plugin_status()

            # 为每个插件启动独立的定时器
            self.start_all_plugin_timers()

        else:
            # 停止所有插件的定时器
            self.stop_all_plugin_timers()

            # 禁用所有激活的插件
            self.deactivate_all_plugins()

            print("插件去重控制已禁用")

    def create_all_plugin_filters(self, source_name):
        """创建所有插件滤镜"""
        print("开始创建所有插件滤镜...")

        for plugin in self.plugin_dedup_control["plugins"]:
            filter_name = plugin["filter_name"]
            plugin_path = plugin["plugin_path"]
            filter_kind = plugin["filter_kind"]

            # 检查插件文件是否存在，如果不存在则尝试重新加载
            if not os.path.exists(plugin_path):
                print(f"警告：VST插件文件不存在: {plugin_path}")
                print(f"尝试重新加载插件: {plugin['display_name']}")

                # 尝试重新加载插件
                if self.reload_plugin_filter(source_name, plugin):
                    print(f"✅ 插件 {plugin['display_name']} 重新加载成功")
                else:
                    print(f"❌ 插件 {plugin['display_name']} 重新加载失败，跳过此插件")
                    continue

            # 检查滤镜是否已存在
            existing_response = self.send_request_and_get_response("GetSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name
            }, timeout=2)

            if existing_response and existing_response.get("requestStatus", {}).get("result"):
                print(f"滤镜 '{filter_name}' 已存在，确保其处于禁用状态")
                # 确保滤镜是禁用状态
                self.set_filter_enabled(source_name, filter_name, False)
                continue

            # 创建VST滤镜
            print(f"创建VST滤镜: {filter_name}")
            filter_settings = {
                "plugin_path": plugin_path
            }

            response = self.send_request_and_get_response("CreateSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterKind": filter_kind,
                "filterSettings": filter_settings
            }, timeout=5)

            if response and response.get("requestStatus", {}).get("result"):
                print(f"✅ 成功创建VST滤镜: {filter_name}")
                # 创建后立即禁用
                import time
                time.sleep(0.5)  # 等待滤镜完全创建
                self.set_filter_enabled(source_name, filter_name, False)
            else:
                error_msg = response.get("requestStatus", {}).get("comment", "未知错误") if response else "无响应"
                print(f"❌ 创建VST滤镜失败: {filter_name}, 错误: {error_msg}")
                # 不返回False，继续尝试其他插件
                continue

        print("插件滤镜创建过程完成")
        return True

    def set_filter_enabled(self, source_name, filter_name, enabled):
        """启用或禁用滤镜"""
        response = self.send_request_and_get_response("SetSourceFilterEnabled", {
            "sourceName": source_name,
            "filterName": filter_name,
            "filterEnabled": enabled
        }, timeout=2)

        if response and response.get("requestStatus", {}).get("result"):
            status = "启用" if enabled else "禁用"
            print(f"✅ {status}滤镜成功: {filter_name}")
            return True
        else:
            status = "启用" if enabled else "禁用"
            error_msg = response.get("requestStatus", {}).get("comment", "未知错误") if response else "无响应"
            print(f"❌ {status}滤镜失败: {filter_name}, 错误: {error_msg}")
            return False

    def start_all_plugin_timers(self):
        """启动所有插件的定时器"""
        print("启动所有插件的独立定时器...")

        for i, plugin in enumerate(self.plugin_dedup_control["plugins"]):
            # 为每个插件安排第一次激活
            self.schedule_plugin_activation(i)
            print(f"已安排插件 {plugin['display_name']} 的定时器")

    def stop_all_plugin_timers(self):
        """停止所有插件的定时器"""
        print("停止所有插件的定时器...")

        for plugin in self.plugin_dedup_control["plugins"]:
            plugin["timer"].stop()
            plugin["duration_timer"].stop()
            plugin["is_active"] = False

    def schedule_plugin_activation(self, plugin_index, *args):
        """安排指定插件的激活"""
        if not self.plugin_dedup_control["enabled"]:
            return

        if plugin_index >= len(self.plugin_dedup_control["plugins"]):
            return

        plugin = self.plugin_dedup_control["plugins"][plugin_index]

        # 随机选择激活间隔
        min_interval = plugin["min_interval_secs"]
        max_interval = plugin["max_interval_secs"]
        interval = random.uniform(min_interval, max_interval)

        interval_ms = int(interval * 1000)
        plugin["timer"].singleShot(interval_ms, lambda: self.activate_plugin_by_index(plugin_index))
        print(f"安排插件 {plugin['display_name']} 在 {interval:.1f}秒后激活")

    def activate_plugin_by_index(self, plugin_index):
        """激活指定索引的插件"""
        if not self.plugin_dedup_control["enabled"]:
            return

        if plugin_index >= len(self.plugin_dedup_control["plugins"]):
            return

        plugin = self.plugin_dedup_control["plugins"][plugin_index]
        source_name = self.plugin_dedup_control["source_name"]
        filter_name = plugin["filter_name"]

        print(f"激活插件: {plugin['display_name']} ({filter_name})")

        # 启用插件滤镜
        if self.set_filter_enabled(source_name, filter_name, True):
            plugin["is_active"] = True

            # 随机选择持续时间
            min_duration = plugin["min_duration_secs"]
            max_duration = plugin["max_duration_secs"]
            duration = random.uniform(min_duration, max_duration)

            duration_ms = int(duration * 1000)
            print(f"插件 {plugin['display_name']} 将在 {duration:.1f} 秒后禁用")
            plugin["duration_timer"].singleShot(duration_ms, lambda: self.deactivate_plugin_by_index(plugin_index))
        else:
            print(f"激活插件失败: {filter_name}")
            print(f"尝试重新加载插件: {plugin['display_name']}")

            # 如果激活失败，尝试重新加载插件
            if self.reload_plugin_filter(source_name, plugin):
                print(f"插件重新加载成功，重新尝试激活: {plugin['display_name']}")
                # 重新尝试激活
                if self.set_filter_enabled(source_name, filter_name, True):
                    plugin["is_active"] = True

                    # 随机选择持续时间
                    min_duration = plugin["min_duration_secs"]
                    max_duration = plugin["max_duration_secs"]
                    duration = random.uniform(min_duration, max_duration)

                    duration_ms = int(duration * 1000)
                    print(f"插件 {plugin['display_name']} 将在 {duration:.1f} 秒后禁用")
                    plugin["duration_timer"].singleShot(duration_ms, lambda: self.deactivate_plugin_by_index(plugin_index))
                else:
                    print(f"重新加载后仍无法激活插件: {filter_name}")
                    # 重新安排下次激活
                    self.schedule_plugin_activation(plugin_index)
            else:
                print(f"插件重新加载失败: {plugin['display_name']}")
                # 重新安排下次激活
                self.schedule_plugin_activation(plugin_index)

    def deactivate_plugin_by_index(self, plugin_index):
        """禁用指定索引的插件"""
        if plugin_index >= len(self.plugin_dedup_control["plugins"]):
            return

        plugin = self.plugin_dedup_control["plugins"][plugin_index]
        source_name = self.plugin_dedup_control["source_name"]
        filter_name = plugin["filter_name"]

        if plugin["is_active"]:
            print(f"禁用插件: {plugin['display_name']} ({filter_name})")
            self.set_filter_enabled(source_name, filter_name, False)
            plugin["is_active"] = False

        # 如果插件去重仍然启用，安排下一次激活
        if self.plugin_dedup_control["enabled"]:
            self.schedule_plugin_activation(plugin_index)

    def deactivate_all_plugins(self):
        """禁用所有插件"""
        source_name = self.plugin_dedup_control["source_name"]

        for plugin in self.plugin_dedup_control["plugins"]:
            if plugin["is_active"]:
                filter_name = plugin["filter_name"]
                self.set_filter_enabled(source_name, filter_name, False)
                plugin["is_active"] = False
                print(f"禁用插件: {plugin['display_name']}")

        print("所有插件已禁用")

    def reload_plugin_filter(self, source_name, plugin):
        """重新加载插件滤镜"""
        filter_name = plugin["filter_name"]
        plugin_path = plugin["plugin_path"]
        filter_kind = plugin["filter_kind"]

        print(f"🔄 开始重新加载插件滤镜: {filter_name}")

        try:
            # 1. 先删除现有滤镜（如果存在）
            existing_response = self.send_request_and_get_response("GetSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name
            }, timeout=2)

            if existing_response and existing_response.get("requestStatus", {}).get("result"):
                print(f"删除现有滤镜: {filter_name}")
                delete_response = self.send_request_and_get_response("RemoveSourceFilter", {
                    "sourceName": source_name,
                    "filterName": filter_name
                }, timeout=2)

                if delete_response and delete_response.get("requestStatus", {}).get("result"):
                    print(f"✅ 成功删除现有滤镜: {filter_name}")
                    import time
                    time.sleep(1)  # 等待删除完成
                else:
                    print(f"⚠️ 删除现有滤镜失败: {filter_name}")

            # 2. 检查插件文件是否现在存在
            if not os.path.exists(plugin_path):
                print(f"❌ 插件文件仍然不存在: {plugin_path}")
                return False

            # 3. 重新创建滤镜
            print(f"重新创建VST滤镜: {filter_name}")
            filter_settings = {
                "plugin_path": plugin_path
            }

            response = self.send_request_and_get_response("CreateSourceFilter", {
                "sourceName": source_name,
                "filterName": filter_name,
                "filterKind": filter_kind,
                "filterSettings": filter_settings
            }, timeout=5)

            if response and response.get("requestStatus", {}).get("result"):
                print(f"✅ 成功重新创建VST滤镜: {filter_name}")
                # 创建后立即禁用
                import time
                time.sleep(0.5)  # 等待滤镜完全创建
                self.set_filter_enabled(source_name, filter_name, False)
                return True
            else:
                error_msg = response.get("requestStatus", {}).get("comment", "未知错误") if response else "无响应"
                print(f"❌ 重新创建VST滤镜失败: {filter_name}, 错误: {error_msg}")
                return False

        except Exception as e:
            print(f"❌ 重新加载插件时出现异常: {e}")
            import traceback
            traceback.print_exc()
            return False

    def download_plugin(self, download_url, plugin_name):
        """下载插件"""
        print(f"🔗 准备下载插件: {plugin_name}")
        print(f"下载地址: {download_url}")

        try:
            # 使用系统默认浏览器打开下载链接
            import webbrowser
            webbrowser.open(download_url)

            # 显示下载提示对话框
            from PyQt5.QtWidgets import QMessageBox
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Information)
            msg.setWindowTitle("插件下载")
            msg.setText(f"正在为您打开 {plugin_name} 的下载页面")
            msg.setInformativeText(
                "下载完成后，请将插件文件放置到以下路径：\n"
                "C:\\Program Files\\VSTPlugins\\\n\n"
                "然后重新启用插件去重功能，系统会自动检测插件文件。"
            )
            msg.setStandardButtons(QMessageBox.Ok)
            msg.exec_()

            print(f"✅ 已打开 {plugin_name} 的下载页面")

        except Exception as e:
            print(f"❌ 打开下载页面失败: {e}")

            # 显示错误对话框
            from PyQt5.QtWidgets import QMessageBox
            msg = QMessageBox()
            msg.setIcon(QMessageBox.Warning)
            msg.setWindowTitle("下载失败")
            msg.setText(f"无法打开 {plugin_name} 的下载页面")
            msg.setInformativeText(
                f"请手动复制以下链接到浏览器中下载：\n\n{download_url}\n\n"
                "下载完成后，请将插件文件放置到：\nC:\\Program Files\\VSTPlugins\\"
            )
            msg.setStandardButtons(QMessageBox.Ok)
            msg.exec_()

    def refresh_plugin_status(self, *args):
        """刷新插件状态，更新下载按钮显示"""
        print("🔄 刷新插件状态...")

        for i, plugin in enumerate(self.plugin_dedup_control["plugins"]):
            if i < len(self.plugin_controls):
                controls = self.plugin_controls[i]
                download_btn = controls.get('download_btn')
                status_label = controls.get('status_label')

                if download_btn and status_label:
                    # 检查插件文件是否存在
                    plugin_exists = os.path.exists(plugin['plugin_path'])

                    if plugin_exists:
                        # 插件存在，隐藏下载按钮，显示已安装状态
                        download_btn.hide()
                        status_label.setText("✅ 已安装")
                        status_label.setStyleSheet("color: #10b981; font-weight: bold; font-size: 9pt;")
                        print(f"✅ 插件 {plugin['display_name']} 已安装")
                    else:
                        # 插件不存在，显示下载按钮，显示未安装状态
                        download_btn.show()
                        status_label.setText("❌ 未安装")
                        status_label.setStyleSheet("color: #ef4444; font-weight: bold; font-size: 9pt;")
                        print(f"❌ 插件 {plugin['display_name']} 未安装")

        print("✅ 插件状态刷新完成")

    def toggle_audio_mute_control(self, state):
        """切换断音控制状态"""
        is_enabled = state == Qt.Checked
        print(f"断音控制状态切换: {is_enabled}")
        
        self.audio_mute_control["enabled"] = is_enabled
        
        if is_enabled:
            # 检查连接状态
            if not self.is_connected:
                print("错误：未连接到 OBS，无法启用断音控制")
                self.audio_mute_checkbox.setChecked(False)
                return
            
            # 使用统一的音频媒体源
            source = self.audio_source_combo.currentData()
            if source:
                self.audio_mute_control["source_name"] = source
            else:
                source = self.audio_mute_control["source_name"]

            if not source:
                print("错误：请先选择音频媒体源")
                self.audio_mute_checkbox.setChecked(False)
                return
            
            print(f"启用断音控制，媒体源: {source}")

            # 更新断音控制参数为用户设置的值
            self.audio_mute_control["min_interval_secs"] = self.audio_mute_interval_min_spin.value()
            self.audio_mute_control["max_interval_secs"] = self.audio_mute_interval_max_spin.value()
            self.audio_mute_control["min_mute_duration_secs"] = self.audio_mute_duration_min_spin.value()
            self.audio_mute_control["max_mute_duration_secs"] = self.audio_mute_duration_max_spin.value()

            print(f"断音参数 - 间隔: {self.audio_mute_control['min_interval_secs']:.1f}-{self.audio_mute_control['max_interval_secs']:.1f}秒, 持续: {self.audio_mute_control['min_mute_duration_secs']:.1f}-{self.audio_mute_control['max_mute_duration_secs']:.1f}秒")

            # 保存原始音量
            self.save_original_volume(source)

            # 启动定时器
            self.schedule_next_mute()
            
        else:
            print("禁用断音控制")
            # 停止所有相关定时器
            self.audio_mute_control["timer"].stop()
            self.audio_mute_control["mute_timer"].stop()

            # 恢复原始音量
            if self.audio_mute_control["is_muted"]:
                print("断音控制禁用时恢复音量")
                self.restore_original_volume()

            print("断音控制已完全停止")
            self.audio_mute_control["is_muted"] = False

    def save_original_volume(self, source_name):
        """保存原始音量"""
        def on_response(data):
            print(f"GetInputVolume响应: {data}")
            if data.get("responseData"):
                volume = data["responseData"].get("inputVolumeMul", 1.0)
                self.audio_mute_control["original_volume"] = volume
                print(f"保存原始音量: {volume}")
            else:
                print("无法获取原始音量，使用默认值 1.0")
                self.audio_mute_control["original_volume"] = 1.0
        
        print(f"发送GetInputVolume请求: inputName={source_name}")
        self.send_obs_request("GetInputVolume", {"inputName": source_name}, on_response)

    def restore_original_volume(self, *args):
        """恢复原始音量"""
        source_name = self.audio_mute_control["source_name"]
        original_volume = self.audio_mute_control["original_volume"]
        
        print(f"发送SetInputVolume请求: inputName={source_name}, inputVolumeMul={original_volume}")
        self.send_obs_request("SetInputVolume", {
            "inputName": source_name,
            "inputVolumeMul": original_volume
        })
        
        self.audio_mute_control["is_muted"] = False
        print(f"恢复原始音量: {original_volume}")

    def schedule_next_mute(self, *args):
        """安排下一次断音"""
        if not self.audio_mute_control["enabled"]:
            print("断音控制已禁用，取消安排")
            return

        # 随机选择断音间隔
        min_interval = self.audio_mute_control["min_interval_secs"]
        max_interval = self.audio_mute_control["max_interval_secs"]
        interval = random.uniform(min_interval, max_interval)

        interval_ms = int(interval * 1000)
        self.audio_mute_control["timer"].singleShot(interval_ms, self.execute_mute)
        print(f"安排下一次断音，间隔: {interval:.1f}秒 (范围: {min_interval:.1f}-{max_interval:.1f}秒)")

    def execute_mute(self, *args):
        """执行断音"""
        print(f"execute_mute 被调用，enabled: {self.audio_mute_control['enabled']}, is_muted: {self.audio_mute_control['is_muted']}")
        
        if not self.audio_mute_control["enabled"] or self.audio_mute_control["is_muted"]:
            print("断音条件不满足，退出")
            return
        
        source_name = self.audio_mute_control["source_name"]
        print(f"准备执行断音，媒体源: {source_name}")
        
        # 设置音量为0（断音）
        print(f"发送SetInputVolume请求，inputName: {source_name}, inputVolumeMul: 0.0")
        self.send_obs_request("SetInputVolume", {
            "inputName": source_name,
            "inputVolumeMul": 0.0
        })
        
        self.audio_mute_control["is_muted"] = True
        print(f"执行断音成功: {source_name}")
        
        # 随机选择断音持续时间
        min_duration = self.audio_mute_control["min_mute_duration_secs"]
        max_duration = self.audio_mute_control["max_mute_duration_secs"]
        duration = random.uniform(min_duration, max_duration)

        duration_ms = int(duration * 1000)
        print(f"安排 {duration:.1f} 秒后恢复音量 (范围: {min_duration:.1f}-{max_duration:.1f}秒)")
        self.audio_mute_control["mute_timer"].singleShot(duration_ms, self.restore_original_volume_and_schedule_next)

    def restore_original_volume_and_schedule_next(self, *args):
        """恢复原始音量并安排下一次断音"""
        print("restore_original_volume_and_schedule_next 被调用")
        self.restore_original_volume()
        self.schedule_next_mute()

    # --- 随机音频播放大小控制功能 ---
    def update_audio_volume_setting(self, key, value):
        """更新随机音频播放大小控制设置"""
        print(f"更新随机音频播放大小设置: {key} = {value}")
        self.audio_volume_control[key] = value
        # 立即保存设置到磁盘
        self.save_settings()
        
        # 验证范围设置
        if key.startswith("volume_min_percent"):
            max_key = "volume_max_percent"
            param = "音量百分比"
            if value > self.audio_volume_control.get(max_key, value):
                print(f"警告：{param} 的最小值 ({value}) 不能大于最大值 ({self.audio_volume_control.get(max_key)}). 自动调整最大值。")
                self.audio_volume_control[max_key] = value
        elif key.startswith("volume_max_percent"):
            min_key = "volume_min_percent"
            param = "音量百分比"
            if value < self.audio_volume_control.get(min_key, value):
                print(f"警告：{param} 的最大值 ({value}) 不能小于最小值 ({self.audio_volume_control.get(min_key)}). 自动调整最小值。")
                self.audio_volume_control[min_key] = value
        elif key.startswith("min_interval_secs"):
            max_key = "max_interval_secs"
            param = "调整间隔"
            if value > self.audio_volume_control.get(max_key, value):
                print(f"警告：{param} 的最小值 ({value:.1f}) 不能大于最大值 ({self.audio_volume_control.get(max_key):.1f}). 自动调整最大值。")
                self.audio_volume_control[max_key] = value
        elif key.startswith("max_interval_secs"):
            min_key = "min_interval_secs"
            param = "调整间隔"
            if value < self.audio_volume_control.get(min_key, value):
                print(f"警告：{param} 的最大值 ({value:.1f}) 不能小于最小值 ({self.audio_volume_control.get(min_key):.1f}). 自动调整最小值。")
                self.audio_volume_control[min_key] = value

    def toggle_audio_volume_control(self, state):
        """切换随机音频播放大小控制状态"""
        is_enabled = state == Qt.Checked
        print(f"随机音频播放大小控制状态切换: {is_enabled}")
        
        self.audio_volume_control["enabled"] = is_enabled
        
        if is_enabled:
            # 检查连接状态
            if not self.is_connected:
                print("错误：未连接到 OBS，无法启用随机音频播放大小控制")
                self.audio_volume_checkbox.setChecked(False)
                return
            
            # 使用统一的音频媒体源
            source = self.audio_source_combo.currentData()
            if source:
                self.audio_volume_control["source_name"] = source
            else:
                source = self.audio_volume_control["source_name"]

            if not source:
                print("错误：请先选择音频媒体源")
                self.audio_volume_checkbox.setChecked(False)
                return
            
            print(f"启用随机音频播放大小控制，媒体源: {source}")
            
            # 保存原始音量
            self.save_original_volume_for_volume_control(source)
            
            # 启动定时器
            self.schedule_next_volume_change()
            
        else:
            print("禁用随机音频播放大小控制")
            # 停止定时器
            self.audio_volume_control["timer"].stop()
            
            # 恢复原始音量
            if self.audio_volume_control["is_active"]:
                self.restore_original_volume_for_volume_control()
                self.audio_volume_control["is_active"] = False

    def save_original_volume_for_volume_control(self, source_name):
        """保存原始音量（用于随机音频播放大小控制）"""
        def on_response(data):
            print(f"GetInputVolume响应 (随机音频播放大小): {data}")
            if data.get("responseData"):
                volume = data["responseData"].get("inputVolumeMul", 1.0)
                self.audio_volume_control["original_volume"] = volume
                print(f"保存原始音量 (随机音频播放大小): {volume}")
            else:
                print("无法获取原始音量，使用默认值 1.0")
                self.audio_volume_control["original_volume"] = 1.0
        
        print(f"发送GetInputVolume请求 (随机音频播放大小): inputName={source_name}")
        self.send_obs_request("GetInputVolume", {"inputName": source_name}, on_response)

    def restore_original_volume_for_volume_control(self, *args):
        """恢复原始音量（用于随机音频播放大小控制）"""
        source_name = self.audio_volume_control["source_name"]
        original_volume = self.audio_volume_control["original_volume"]
        
        print(f"发送SetInputVolume请求 (随机音频播放大小): inputName={source_name}, inputVolumeMul={original_volume}")
        self.send_obs_request("SetInputVolume", {
            "inputName": source_name,
            "inputVolumeMul": original_volume
        })
        
        self.audio_volume_control["is_active"] = False
        print(f"恢复原始音量 (随机音频播放大小): {original_volume}")

    def schedule_next_volume_change(self, *args):
        """安排下一次音量调整"""
        if not self.audio_volume_control["enabled"]:
            return
        
        # 随机选择调整间隔
        min_interval = self.audio_volume_control["min_interval_secs"]
        max_interval = self.audio_volume_control["max_interval_secs"]
        interval = random.uniform(min_interval, max_interval)
        
        interval_ms = int(interval * 1000)
        self.audio_volume_control["timer"].singleShot(interval_ms, self.execute_volume_change)
        print(f"安排下一次音量调整，间隔: {interval:.1f}秒")

    def execute_volume_change(self, *args):
        """执行音量调整"""
        print(f"execute_volume_change 被调用，enabled: {self.audio_volume_control['enabled']}, is_active: {self.audio_volume_control['is_active']}")
        
        if not self.audio_volume_control["enabled"]:
            print("随机音频播放大小控制未启用，退出")
            return
        
        source_name = self.audio_volume_control["source_name"]
        print(f"准备执行音量调整，媒体源: {source_name}")
        
        # 生成随机音量百分比
        min_percent = self.audio_volume_control["volume_min_percent"]
        max_percent = self.audio_volume_control["volume_max_percent"]
        print(f"🎚️ 音量范围: {min_percent}% - {max_percent}%")

        # 确保最小值不大于最大值
        if min_percent > max_percent:
            print(f"⚠️ 警告：最小值({min_percent}%)大于最大值({max_percent}%)，交换数值")
            min_percent, max_percent = max_percent, min_percent

        volume_percent = random.randint(min_percent, max_percent)
        
        # 转换为0.0-1.0范围
        volume_mul = volume_percent / 100.0
        
        # 设置音量
        print(f"发送SetInputVolume请求，inputName: {source_name}, inputVolumeMul: {volume_mul} ({volume_percent}%)")
        self.send_obs_request("SetInputVolume", {
            "inputName": source_name,
            "inputVolumeMul": volume_mul
        })
        
        self.audio_volume_control["is_active"] = True
        print(f"执行音量调整成功: {source_name} -> {volume_percent}%")
        
        # 安排下一次调整
        self.schedule_next_volume_change()



# --- 新增：一键启动功能选择对话框 --- #
class QuickStartDialog(QDialog):
    """一键启动功能选择对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("一键启动 - 选择功能")
        self.setMinimumSize(500, 600)
        self.setModal(True)

        # 存储选择的功能
        self.selected_functions = []

        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title = QLabel("🚀 选择要启动的去重功能")
        title.setStyleSheet("""
            QLabel {
                font-size: 16pt;
                font-weight: bold;
                color: #1e293b;
                padding: 15px;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 12px;
                border: 2px solid #e2e8f0;
            }
        """)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # 功能选择区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(12)

        # 创建功能复选框
        self.function_checkboxes = {}

        # 视频功能组
        video_group = QGroupBox("🎥 视频去重功能")
        video_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12pt;
                color: #374151;
                border: 2px solid #d1d5db;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: white;
            }
        """)
        video_layout = QVBoxLayout(video_group)

        video_functions = [
            ("speed", "⚡ 智能加减速", "随机调整播放速度，避免重复检测"),
            ("blur", "🌫️ 模糊去重", "动态调整模糊效果，增加画面变化"),
            ("transform", "↔️ 移动去重", "随机移动画面位置，避免固定构图"),
            ("color", "🎨 颜色去重", "调整色调、亮度等参数，改变画面色彩")
        ]

        for func_id, name, desc in video_functions:
            checkbox = QCheckBox(name)
            checkbox.setStyleSheet(self.get_checkbox_style())

            # 添加描述标签
            desc_label = QLabel(desc)
            desc_label.setStyleSheet("""
                QLabel {
                    color: #6b7280;
                    font-size: 10pt;
                    margin-left: 25px;
                    margin-bottom: 8px;
                }
            """)

            video_layout.addWidget(checkbox)
            video_layout.addWidget(desc_label)
            self.function_checkboxes[func_id] = checkbox

        scroll_layout.addWidget(video_group)

        # 音频功能组
        audio_group = QGroupBox("🔊 音频去重功能")
        audio_group.setStyleSheet(video_group.styleSheet())
        audio_layout = QVBoxLayout(audio_group)

        audio_functions = [
            ("audio_eq", "🎵 音频EQ去重", "调整音频均衡器参数，改变音频特征"),
            ("compressor", "🗜️ 自动压缩", "动态调整音频压缩参数，改变音频动态范围"),
            ("gain", "🎚️ 自动增益", "随机调整音频增益，改变音频响度"),
            ("audio_mute", "🔇 断音控制", "随机静音片段，增加音频变化"),
            ("audio_volume", "🔊 音量控制", "随机调整音量大小，避免固定音量"),
            ("plugin_dedup", "🎛️ 插件去重", "循环启用VST插件，增加音频效果变化")
        ]

        for func_id, name, desc in audio_functions:
            checkbox = QCheckBox(name)
            checkbox.setStyleSheet(self.get_checkbox_style())

            desc_label = QLabel(desc)
            desc_label.setStyleSheet("""
                QLabel {
                    color: #6b7280;
                    font-size: 10pt;
                    margin-left: 25px;
                    margin-bottom: 8px;
                }
            """)

            audio_layout.addWidget(checkbox)
            audio_layout.addWidget(desc_label)
            self.function_checkboxes[func_id] = checkbox

        scroll_layout.addWidget(audio_group)

        # 全选/全不选按钮
        select_layout = QHBoxLayout()
        select_all_btn = QPushButton("全选")
        select_all_btn.setStyleSheet(self.get_button_style("#3b82f6"))
        select_all_btn.clicked.connect(self.select_all)

        select_none_btn = QPushButton("全不选")
        select_none_btn.setStyleSheet(self.get_button_style("#6b7280"))
        select_none_btn.clicked.connect(self.select_none)

        select_layout.addWidget(select_all_btn)
        select_layout.addWidget(select_none_btn)
        select_layout.addStretch()

        scroll_layout.addLayout(select_layout)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)
        layout.addWidget(scroll_area)

        # 按钮区域
        button_layout = QHBoxLayout()

        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet(self.get_button_style("#6b7280"))
        cancel_btn.clicked.connect(self.reject)

        start_btn = QPushButton("开始启动")
        start_btn.setStyleSheet(self.get_button_style("#10b981"))
        start_btn.clicked.connect(self.accept_selection)

        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(start_btn)

        layout.addLayout(button_layout)

    def get_checkbox_style(self):
        return """
            QCheckBox {
                font-weight: bold;
                color: #1e293b;
                spacing: 8px;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 2px solid #cbd5e1;
            }
            QCheckBox::indicator:checked {
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                border-color: #059669;
            }
            QCheckBox::indicator:hover {
                border-color: #667eea;
            }
        """

    def get_button_style(self, color):
        return f"""
            QPushButton {{
                background: {color};
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                min-width: 80px;
            }}
            QPushButton:hover {{
                background: {color}dd;
                transform: translateY(-1px);
            }}
            QPushButton:pressed {{
                background: {color}bb;
                transform: translateY(0px);
            }}
        """

    def select_all(self, *args):
        for checkbox in self.function_checkboxes.values():
            checkbox.setChecked(True)

    def select_none(self, *args):
        for checkbox in self.function_checkboxes.values():
            checkbox.setChecked(False)

    def accept_selection(self, *args):
        self.selected_functions = []
        for func_id, checkbox in self.function_checkboxes.items():
            if checkbox.isChecked():
                self.selected_functions.append(func_id)

        if not self.selected_functions:
            QMessageBox.warning(self, "提示", "请至少选择一个功能！")
            return

        self.accept()

# --- 新增：激活对话框 (重新设计) --- #
class ActivationDialog(QDialog):
    """软件激活对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("软件激活")
        self.setMinimumSize(450, 300)
        self.setModal(True)

        self.settings = QSettings('OBSController', 'Settings')

        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # Logo 图片
        logo_label = QLabel()
        logo_label.setAlignment(Qt.AlignCenter)
        try:
            script_dir = os.path.dirname(os.path.abspath(__file__))
            logo_path = os.path.join(script_dir, "zuozhe.png")
            # print(f"尝试加载 Logo: {logo_path}")

            logo_pixmap = QPixmap(logo_path)
            if not logo_pixmap.isNull():
                scaled_pixmap = logo_pixmap.scaledToWidth(150, Qt.SmoothTransformation)
                logo_label.setPixmap(scaled_pixmap)
            else:
                print(f"警告：无法加载 Logo 图片，Pixmap 为空: {logo_path}")
                logo_label.setText("[Logo 加载失败]")
        except Exception as e:
            print(f"加载 Logo 时发生异常: {e}")
            logo_label.setText("[Logo 加载异常]")
        layout.addWidget(logo_label)

        # 卡密输入区域
        key_layout = QHBoxLayout()
        key_label = QLabel("卡密：")
        self.key_input = QLineEdit()
        self.key_input.setPlaceholderText("请输入卡密")
        key_layout.addWidget(key_label)
        key_layout.addWidget(self.key_input)
        layout.addLayout(key_layout)

        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 登录按钮
        self.login_button = QPushButton("激活")
        self.login_button.clicked.connect(self.accept)
        
        # 解绑按钮
        self.unbind_button = QPushButton("解绑")
        self.unbind_button.clicked.connect(self.handle_unbind)
        
        button_layout.addWidget(self.login_button)
        button_layout.addWidget(self.unbind_button)
        layout.addLayout(button_layout)

        # 状态信息标签
        self.status_label = QLabel()
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setStyleSheet("color: #666666;")
        layout.addWidget(self.status_label)

        # 加载当前激活信息
        self.load_activation_info()

    def load_activation_info(self):
        """加载当前激活信息"""
        current_key = self.settings.value("activation/key", "")
        expiry_date = self.settings.value("activation/expiry_date", "")
        if current_key and expiry_date:
            try:
                expiry = datetime.fromisoformat(expiry_date)
                remaining_days = (expiry - datetime.now()).days
                self.status_label.setText(f"当前激活码: {current_key}\n"
                                        f"到期时间: {expiry.strftime('%Y-%m-%d %H:%M:%S')}\n"
                                        f"剩余天数: {remaining_days} 天")
                self.key_input.setText(current_key)  # 自动填入当前卡密
            except Exception as e:
                print(f"加载激活信息出错: {e}")
                self.status_label.setText("加载激活信息出错")
        else:
            self.status_label.setText("软件未激活\n请输入卡密进行激活\n如需购买卡密请联系客服")
            self.status_label.setStyleSheet("color: #FF4444; font-weight: bold;")

    def handle_unbind(self, checked=False):
        try:
            # 获取输入的卡密
            key_to_unbind = self.key_input.text().strip()
            if not key_to_unbind:
                QMessageBox.warning(self, "解绑失败", "请输入要解绑的卡密")
                return

            # 获取云端数据
            headers = {
                'Authorization': f'token {GITEE_TOKEN}',
                'Content-Type': 'application/json;charset=UTF-8'
            }
            
            response = requests.get(
                f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/{GENERATED_KEYS_FILE}',
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                content = base64.b64decode(response.json()['content']).decode('utf-8')
                keys_data = json.loads(content)
                
                # 查找卡密信息
                hash_key = hashlib.sha256(key_to_unbind.encode()).hexdigest()
                key_info = None
                for info in keys_data:
                    if info.get("hash", "") == hash_key:
                        key_info = info
                        break
                
                if not key_info:
                    QMessageBox.warning(self, "解绑失败", "找不到该卡密信息")
                    return

                # 检查卡密状态
                if key_info.get("status") != "已激活":
                    QMessageBox.warning(self, "解绑失败", "该卡密未激活，无需解绑")
                    return

                if not key_info.get("machine_code"):
                    QMessageBox.warning(self, "解绑失败", "该卡密未绑定设备，无需解绑")
                    return

                # 获取或初始化解绑次数
                unbind_count = key_info.get("unbind_count", 0)
                
                # 确认对话框显示扣除天数信息
                deduct_days = 0
                if unbind_count >= 3:
                    deduct_days = 1
                    msg = f"这是第 {unbind_count + 1} 次解绑，将扣除 {deduct_days} 天使用时间。\n是否继续？"
                else:
                    msg = f"这是第 {unbind_count + 1} 次解绑，不会扣除使用时间。\n是否继续？"

                reply = QMessageBox.question(self, "确认解绑", msg,
                                          QMessageBox.Yes | QMessageBox.No,
                                          QMessageBox.No)
                
                if reply == QMessageBox.Yes:
                    # 更新解绑次数和到期时间
                    key_info["unbind_count"] = unbind_count + 1
                    key_info["machine_code"] = ""  # 清除机器码
                    
                    if deduct_days > 0:
                        # 更新到期时间
                        expiry_time = datetime.strptime(key_info["expiry_time"], "%Y-%m-%d %H:%M:%S")
                        new_expiry_time = expiry_time - timedelta(days=deduct_days)
                        key_info["expiry_time"] = new_expiry_time.strftime("%Y-%m-%d %H:%M:%S")

                    # 更新云端数据
                    updated_content = json.dumps(keys_data, ensure_ascii=False, indent=2)
                    updated_content_base64 = base64.b64encode(updated_content.encode('utf-8')).decode('utf-8')
                    
                    update_response = requests.put(
                        f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/{GENERATED_KEYS_FILE}',
                        headers=headers,
                        json={
                            'message': f'解绑卡密: {key_to_unbind}',
                            'content': updated_content_base64,
                            'sha': response.json()['sha']
                        },
                        timeout=10
                    )
                    
                    if update_response.status_code == 200:
                        # 如果解绑的是当前激活的卡密，清除本地激活信息
                        current_key = self.settings.value("activation/key", "")
                        if current_key == key_to_unbind:
                            self.settings.remove("activation/key")
                            self.settings.remove("activation/expiry_date")
                            self.settings.sync()
                            # 通知主窗口禁用功能
                            if isinstance(self.parent(), MainWindow):
                                self.parent().disable_features()
                        
                        QMessageBox.information(self, "解绑成功", 
                            f"卡密已成功解绑\n"
                            f"已使用 {unbind_count + 1} 次解绑\n" +
                            (f"扣除 {deduct_days} 天使用时间" if deduct_days > 0 else "本次解绑不扣除使用时间"))
                        
                        # 更新状态显示
                        self.load_activation_info()
                    else:
                        QMessageBox.warning(self, "解绑失败", "更新云端数据失败，请重试")
            else:
                QMessageBox.warning(self, "解绑失败", "无法连接到服务器，请检查网络连接")
                
        except Exception as e:
            QMessageBox.warning(self, "解绑失败", f"发生错误：{str(e)}")

    def get_key(self):
        return self.key_input.text().strip()

class AntiDebug:
    def __init__(self):
        self.last_check_time = time.time()
        self.check_interval = 1.0  # 检查间隔（秒）
        
    def is_debugger_present(self):
        """检测调试器"""
        try:
            return ctypes.windll.kernel32.IsDebuggerPresent() != 0
        except:
            return False
            
    def check_debugging_tools(self):
        """检测常见调试工具进程"""
        suspicious_processes = [
            "x64dbg.exe", "x32dbg.exe", "ollydbg.exe", "ida64.exe", 
            "ida.exe", "cheatengine-x86_64.exe", "cheatengine-i386.exe",
            "pestudio.exe", "processhacker.exe"
        ]
        
        for proc in psutil.process_iter(['name']):
            try:
                if proc.info['name'].lower() in suspicious_processes:
                    return True
            except:
                continue
        return False
        
    def check_window_names(self):
        """检测可疑窗口标题"""
        suspicious_titles = [
            "x64dbg", "x32dbg", "ollydbg", "ida", "immunity debugger",
            "cheat engine", "process hacker"
        ]
        
        def check_window(hwnd, ctx):
            if win32gui.IsWindowVisible(hwnd):
                title = win32gui.GetWindowText(hwnd).lower()
                for suspicious in suspicious_titles:
                    if suspicious in title:
                        ctx[0] = True
                        return False
            return True
            
        found = [False]
        try:
            win32gui.EnumWindows(check_window, found)
        except:
            pass
        return found[0]
        
    def perform_security_check(self):
        """执行安全检查"""
        current_time = time.time()
        if current_time - self.last_check_time < self.check_interval:
            return True
            
        self.last_check_time = current_time
        
        # 检测调试器
        if self.is_debugger_present():
            self.handle_security_violation("检测到调试器")
            return False
            
        # 检测调试工具
        if self.check_debugging_tools():
            self.handle_security_violation("检测到调试工具")
            return False
            
        # 检测可疑窗口
        if self.check_window_names():
            self.handle_security_violation("检测到可疑程序")
            return False
            
        return True
        
    def handle_security_violation(self, reason):
        """处理安全违规"""
        print(f"安全警告: {reason}")
        # 可以选择直接退出程序或采取其他措施
        sys.exit(1)

class IntegrityChecker:
    def __init__(self):
        self.last_check_time = time.time()
        self.check_interval = 5.0  # 每5秒检查一次
        self.hash_cache = {}  # 缓存云端哈希值
        self.last_hash_update = 0
        self.hash_update_interval = 3600  # 每小时更新一次云端哈希值
        
    def get_cloud_hashes(self):
        """从云端获取最新的哈希值"""
        try:
            current_time = time.time()
            # 如果缓存的哈希值还在有效期内，直接返回
            if self.hash_cache and (current_time - self.last_hash_update) < self.hash_update_interval:
                return self.hash_cache

            headers = {
                'Authorization': f'token {GITEE_TOKEN}',
                'Content-Type': 'application/json;charset=UTF-8'
            }
            
            # 从Gitee获取哈希值配置文件
            response = requests.get(
                f'https://gitee.com/api/v5/repos/{REPO_OWNER}/{REPO_NAME}/contents/integrity_hashes.json',
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                content = base64.b64decode(response.json()['content']).decode('utf-8')
                hashes = json.loads(content)
                
                # 更新缓存
                self.hash_cache = hashes
                self.last_hash_update = current_time
                
                return hashes
            else:
                print(f"获取云端哈希值失败: {response.status_code}")
                # 如果有缓存，使用缓存的值
                return self.hash_cache if self.hash_cache else {}
                
        except Exception as e:
            print(f"获取云端哈希值出错: {e}")
            # 如果有缓存，使用缓存的值
            return self.hash_cache if self.hash_cache else {}

    def calculate_file_hash(self, filepath):
        """计算文件的SHA256哈希值"""
        try:
            sha256_hash = hashlib.sha256()
            with open(filepath, "rb") as f:
                for byte_block in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(byte_block)
            return sha256_hash.hexdigest()
        except Exception as e:
            print(f"计算文件哈希出错: {e}")
            return None

    def verify_file_integrity(self):
        """验证关键文件的完整性"""
        try:
            # 获取云端哈希值
            expected_hashes = self.get_cloud_hashes()
            if not expected_hashes:
                print("无法获取云端哈希值，跳过完整性检查")
                return True

            # 确定是否在打包环境
            is_frozen = getattr(sys, 'frozen', False)
            
            # 确定基础路径
            if is_frozen:
                base_path = sys._MEIPASS
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))

            # 验证程序版本（支持最低兼容机制，并弹窗提示）
            current_version = VERSION
            if 'min_version' in expected_hashes:
                min_version = expected_hashes['min_version']
                # 只允许 >= min_version 的版本运行
                if current_version < min_version:
                    print(f"程序版本过低: 当前={current_version}, 最低要求={min_version}")
                    if is_frozen:
                        from PyQt5.QtWidgets import QApplication, QMessageBox
                        app = QApplication.instance()
                        if app is None:
                            app = QApplication(sys.argv)
                        QMessageBox.critical(None, "版本过低", f"当前版本：{current_version}\n最低要求：{min_version}\n请升级到新版本后使用。")
                        sys.exit(1)
                    else:
                        print("开发环境：版本过低被忽略")
                        return True
            elif 'version' in expected_hashes:
                # 兼容老机制：只允许唯一版本
                if current_version != expected_hashes['version']:
                    print(f"程序版本不匹配: 当前={current_version}, 期望={expected_hashes['version']}")
                    if is_frozen:
                        from PyQt5.QtWidgets import QApplication, QMessageBox
                        app = QApplication.instance()
                        if app is None:
                            app = QApplication(sys.argv)
                        QMessageBox.critical(None, "版本不匹配", f"当前版本：{current_version}\n期望版本：{expected_hashes['version']}\n请升级到新版本后使用。")
                        sys.exit(1)
                    else:
                        print("开发环境：版本不匹配被忽略")
                        return True

            # 验证资源文件
            resource_files = {
                'zuozhe.png': '二维码图片',
                'obs2.ico': '程序图标'
            }

            for filename, description in resource_files.items():
                if filename not in expected_hashes.get('files', {}):
                    print(f"警告：云端哈希值中未找到{description} ({filename})的哈希值")
                    continue

                full_path = os.path.join(base_path, filename)
                if not os.path.exists(full_path):
                    print(f"错误：{description} ({filename}) 文件不存在")
                    return False

                current_hash = self.calculate_file_hash(full_path)
                expected_hash = expected_hashes['files'][filename]
                
                if current_hash != expected_hash:
                    print(f"错误：{description} ({filename}) 文件被修改")
                    print(f"当前哈希值：{current_hash}")
                    print(f"预期哈希值：{expected_hash}")
                    return False

            return True
                
        except Exception as e:
            print(f"完整性验证出错: {e}")
            if not is_frozen:
                print("开发环境：完整性验证错误被忽略")
                return True
            return False

    def verify_memory_integrity(self):
        """验证内存中的代码段完整性"""
        try:
            # 获取云端内存哈希值
            expected_hashes = self.get_cloud_hashes()
            if not expected_hashes or 'memory' not in expected_hashes:
                return True  # 如果没有配置内存哈希，暂时返回True
                
            # 获取当前进程句柄
            process_handle = win32api.GetCurrentProcess()
            
            # 获取进程基地址
            base_address = ctypes.c_ulong()
            size = ctypes.c_ulong()
            ctypes.windll.kernel32.GetModuleInformation(
                process_handle,
                ctypes.windll.kernel32.GetModuleHandleA(None),
                ctypes.byref(base_address),
                ctypes.byref(size)
            )
            
            # 计算代码段哈希
            code_hash = hashlib.sha256()
            with mmap.mmap(-1, size.value, access=mmap.ACCESS_READ) as mm:
                code_hash.update(mm.read(size.value))
            
            current_hash = code_hash.hexdigest()
            expected_hash = expected_hashes['memory'].get('code_section')
            
            if expected_hash and current_hash != expected_hash:
                print(f"内存代码段哈希不匹配")
                print(f"当前={current_hash}")
                print(f"期望={expected_hash}")
                return False
                
            return True
            
        except Exception as e:
            print(f"内存完整性检查失败: {e}")
            return False

    def perform_integrity_check(self):
        """执行完整性检查"""
        current_time = time.time()
        if current_time - self.last_check_time < self.check_interval:
            return True
            
        self.last_check_time = current_time
        
        # 检查文件完整性
        if not self.verify_file_integrity():
            self.handle_integrity_violation("文件完整性校验失败")
            return False
            
        # 检查内存完整性
        if not self.verify_memory_integrity():
            self.handle_integrity_violation("内存完整性校验失败")
            return False
            
        # 检查文件权限
        if not self.check_file_permissions():
            self.handle_integrity_violation("文件权限异常")
            return False
            
        return True

    def handle_integrity_violation(self, reason):
        """处理完整性验证失败"""
        print(f"完整性警告: {reason}")
        # 可以选择直接退出程序或采取其他措施
        sys.exit(1)

    def check_file_permissions(self):
        """检查关键文件的权限"""
        try:
            # 确定是否在打包环境
            is_frozen = getattr(sys, 'frozen', False)
            
            # 确定基础路径
            if is_frozen:
                base_path = sys._MEIPASS
            else:
                base_path = os.path.dirname(os.path.abspath(__file__))

            # 要检查的文件列表
            files_to_check = [
                'zuozhe.png',
                'obs2.ico'
            ]

            for filename in files_to_check:
                full_path = os.path.join(base_path, filename)
                if not os.path.exists(full_path):
                    continue  # 如果文件不存在，跳过检查

                # 获取文件权限
                try:
                    # 尝试打开文件进行读取
                    with open(full_path, 'rb') as f:
                        f.read(1)  # 尝试读取一个字节
                except PermissionError:
                    print(f"错误：无法读取文件 {filename}")
                    return False
                except Exception as e:
                    print(f"检查文件 {filename} 权限时出错: {e}")
                    return False

            return True
                
        except Exception as e:
            print(f"检查文件权限时出错: {e}")
            if not is_frozen:
                print("开发环境：文件权限检查错误被忽略")
                return True
            return False

class UpdaterThread(QThread):
    """更新下载线程"""
    progress_signal = pyqtSignal(int)
    finished_signal = pyqtSignal(bool, str)

    def __init__(self, url, target_path):
        super().__init__()
        self.url = url
        self.target_path = target_path

    def run(self):
        try:
            response = requests.get(self.url, stream=True)
            total_size = int(response.headers.get('content-length', 0))
            block_size = 1024
            downloaded = 0

            # 用 mkstemp 只拿文件名，不持有句柄
            fd, temp_path = tempfile.mkstemp()
            os.close(fd)  # 立即关闭句柄

            try:
                with open(temp_path, 'wb') as f:
                    for data in response.iter_content(block_size):
                        downloaded += len(data)
                        f.write(data)
                        if total_size:
                            progress = int((downloaded / total_size) * 100)
                            self.progress_signal.emit(progress)

                # 下载完成后，校验文件大小
                if total_size and os.path.getsize(temp_path) != total_size:
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                    self.finished_signal.emit(False, "下载的文件大小与服务器不一致，可能下载不完整。")
                    return

                # 下载完成后，替换原文件（先重命名为 .exe.new）
                if os.path.exists(self.target_path):
                    os.remove(self.target_path)
                shutil.move(temp_path, self.target_path)
                self.finished_signal.emit(True, "更新成功")

            except Exception as e:
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                self.finished_signal.emit(False, f"更新失败: {str(e)}")

        except Exception as e:
            self.finished_signal.emit(False, f"下载失败: {str(e)}")

# --- 定义一个浅色主题 QSS --- #
light_stylesheet = """
    QWidget {
        background-color: #f0f0f0; /* 浅灰色背景 */
        color: #000000; /* 黑色文字 */
        font-size: 11pt; /* 稍微增大字体 */
    }
    /* --- 给 Tab 页内部 QWidget 添加内边距 --- */
    QTabWidget > QWidget > QWidget {
        padding: 10px;
    }
    /* ------------------------------------ */
    QTabWidget::pane {
        border-top: 1px solid #d0d0d0;
    }
    QTabBar::tab {
        background: #e1e1e1;
        color: #000000;
        border: 1px solid #d0d0d0;
        border-bottom: none;
        padding: 6px;
        min-width: 80px;
    }
    QTabBar::tab:selected {
        background: #f0f0f0; /* 选中Tab背景与窗口一致 */
        border: 1px solid #d0d0d0;
        border-top: 2px solid #3498db; /* 蓝色顶部边框表示选中 */
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: -1px;
        color: black; /* 选中时字体为黑色 */
    }
    QTabBar::tab:!selected:hover {
        background: #eaeaea;
    }
    QPushButton {
        background-color: #e1e1e1;
        border: 1px solid #c0c0c0;
        padding: 6px 8px; /* 增加一点垂直内边距 */
        min-height: 18px; /* 适配 padding */
        min-width: 75px;
        border-radius: 3px;
    }
    QPushButton:hover {
        background-color: #efefef;
        border-color: #b0b0b0;
    }
    QPushButton:pressed {
        background-color: #d1d1d1;
    }
    QComboBox {
        background-color: #ffffff;
        border: 1px solid #c0c0c0;
        padding: 4px 5px; /* 增加一点垂直内边距 */
        border-radius: 3px;
    }
    QComboBox::drop-down {
        border: none;
    }
    QComboBox QAbstractItemView {
         background-color: #ffffff;
         selection-background-color: #3498db; /* 蓝色选中背景 */
         selection-color: #ffffff; /* 选中文字为白色 */
         color: #000000;
         border: 1px solid #c0c0c0;
    }
    QLineEdit, QSpinBox, QDoubleSpinBox {
        background-color: #ffffff;
        border: 1px solid #c0c0c0;
        border-radius: 3px;
        min-height: 22px;
    }
    QSlider::groove:horizontal {
        height: 8px;
        background: #d0d0d0;
        border-radius: 4px;
    }
    QSlider::handle:horizontal {
        background: #5dade2; /* 中等蓝色 */
        border: 1px solid #4a99cf; /* 匹配的边框 */
        width: 16px;
        margin: -4px 0;
        border-radius: 8px;
    }
    QSlider::handle:horizontal:hover {
        background: #85c1e9; /* 悬停时更亮一点 */
        border: 1px solid #5dade2;
    }
    QCheckBox::indicator {
        width: 16px;
        height: 16px;
    }
    QCheckBox::indicator:unchecked {
        background-color: #ffffff;
        border: 1px solid #c0c0c0;
        border-radius: 3px;
    }
    QCheckBox::indicator:checked {
        background-color: #3498db; /* 蓝色选中背景 */
        border: 1px solid #3498db;
        border-radius: 3px;
    }
    QLabel {
        background-color: transparent; /* 确保标签背景透明 */
    }
    QFormLayout {
        vertical-spacing: 10px; /* 增加行间距 */
        horizontal-spacing: 15px; /* 增加标签和控件间距 */
    }
    /* --- SpinBox 按钮样式 --- */
    QSpinBox, QDoubleSpinBox {
        padding-right: 20px; /* 稍微调整按钮空间 */
    }
    QSpinBox::up-button, QDoubleSpinBox::up-button {
        subcontrol-origin: border;
        subcontrol-position: top right;
        width: 20px;
        height: 11px;
        background-color: #e8e8e8;
        border: 1px solid #c0c0c0;
        border-left: none;
        border-top: none;
        border-top-right-radius: 3px;
    }
    QSpinBox::up-button:hover, QDoubleSpinBox::up-button:hover {
        background-color: #f0f0f0;
    }
    QSpinBox::down-button, QDoubleSpinBox::down-button {
        subcontrol-origin: border;
        subcontrol-position: bottom right;
        width: 20px;
        height: 11px;
        background-color: #e8e8e8;
        border: 1px solid #c0c0c0;
        border-left: none;
        border-bottom-right-radius: 3px;
    }
    QSpinBox::down-button:hover, QDoubleSpinBox::down-button:hover {
        background-color: #f0f0f0;
    }
"""

# --- 程序入口 ---
if __name__ == '__main__':
    import ctypes
    import os
    
    # 设置更具体的 AppUserModelID
    app_id = 'com.haohnb.obscontroller.1.0'
    try:
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(app_id)
    except Exception as e:
        print(f"设置 AppUserModelID 失败: {e}")
    
    # 创建应用程序实例
    app = QApplication(sys.argv)
    
    # 获取图标的绝对路径
    icon_path = resource_path('obs2.ico')
    if not os.path.exists(icon_path):
        # 如果在当前目录找不到，尝试在脚本所在目录查找
        icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'obs2.ico')
    
    if os.path.exists(icon_path):
        try:
            # 创建图标对象
            app_icon = QIcon(icon_path)
            
            # 设置应用程序图标
            app.setWindowIcon(app_icon)
            
            # 同时也设置任务栏图标
            if hasattr(ctypes, 'windll'):  # 确保在 Windows 系统上
                myappid = app_id  # 使用相同的 app_id
                ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(myappid)
                
            print(f"成功加载图标: {icon_path}")
        except Exception as e:
            print(f"设置图标失败: {e}")
    else:
        print(f"找不到图标文件: {icon_path}")

    # 应用浅色样式表
    app.setStyleSheet(light_stylesheet)

    # 创建主窗口实例
    main_window = MainWindow()

    # 设置窗口图标
    if os.path.exists(icon_path):
        main_window.setWindowIcon(QIcon(icon_path))

    # 显示主窗口
    main_window.show()

    # --- 启动后检查激活状态并处理 --- #
    main_window.check_and_handle_activation()

    sys.exit(app.exec_()) # 进入事件循环



# --- 几何形状播放窗口类 --- #
class GeometricFlashWindow(QWidget):
    """几何形状爆闪播放窗口"""

    close_requested = pyqtSignal()

    def __init__(self):
        super().__init__()
        self.current_color = "#ff0000"  # 默认红色
        self.shapes = []  # 存储当前的几何形状
        self.decoration_lines = []  # 存储装饰线条
        self.generate_random_shapes()

    def generate_random_shapes(self):
        """生成随机几何形状"""
        try:
            import random

            self.shapes = []
            self.decoration_lines = []

            # 获取实际屏幕尺寸
            screen = QApplication.primaryScreen()
            if screen:
                screen_size = screen.size()
                width = screen_size.width()
                height = screen_size.height()
            else:
                width = 1920
                height = 1080

            # 生成3-6个随机多边形（减少数量避免性能问题）
            num_shapes = random.randint(3, 6)

            for _ in range(num_shapes):
                # 生成随机多边形
                num_points = random.randint(3, 6)  # 减少顶点数量
                points = []

                # 随机生成多边形的中心点
                center_x = random.randint(width // 6, 5 * width // 6)
                center_y = random.randint(height // 6, 5 * height // 6)

                # 生成围绕中心点的随机顶点
                for i in range(num_points):
                    angle = (2 * 3.14159 * i) / num_points + random.uniform(-0.3, 0.3)
                    radius = random.randint(80, 250)

                    x = center_x + int(radius * random.uniform(0.7, 1.3) * (1 if i % 2 == 0 else -1))
                    y = center_y + int(radius * random.uniform(0.7, 1.3) * (1 if i % 2 == 0 else -1))

                    # 确保点在屏幕范围内
                    x = max(50, min(width - 50, x))
                    y = max(50, min(height - 50, y))

                    points.append(QPoint(x, y))

                if len(points) >= 3:  # 确保至少有3个点
                    self.shapes.append(QPolygon(points))

            # 预生成装饰线条（避免在paintEvent中生成）
            num_lines = random.randint(8, 12)
            for _ in range(num_lines):
                x1 = random.randint(0, width)
                y1 = random.randint(0, height)
                x2 = random.randint(0, width)
                y2 = random.randint(0, height)
                self.decoration_lines.append((x1, y1, x2, y2))

        except Exception as e:
            print(f"生成形状时出错: {e}")
            # 创建一个简单的默认形状
            self.shapes = [QPolygon([QPoint(100, 100), QPoint(200, 100), QPoint(150, 200)])]
            self.decoration_lines = [(0, 0, 100, 100)]

    def update_color_and_shape(self, color):
        """更新颜色和重新生成形状"""
        try:
            self.current_color = color
            self.generate_random_shapes()  # 每次切换颜色时生成新的形状
            self.update()  # 触发重绘
        except Exception as e:
            print(f"更新颜色和形状时出错: {e}")

    def paintEvent(self, event):
        """绘制几何形状"""
        try:
            painter = QPainter(self)
            painter.setRenderHint(QPainter.Antialiasing)

            # 设置背景色为浅灰色
            painter.fillRect(self.rect(), QColor("#f0f0f0"))

            # 设置画笔和画刷
            color = QColor(self.current_color)
            if not color.isValid():
                color = QColor("#ff0000")  # 默认红色

            painter.setBrush(QBrush(color))
            painter.setPen(QPen(color.darker(120), 2))

            # 绘制所有形状
            for shape in self.shapes:
                if shape.size() >= 3:  # 确保多边形有效
                    painter.drawPolygon(shape)

            # 绘制预生成的装饰线条
            painter.setPen(QPen(color.darker(150), 2))
            for line in self.decoration_lines:
                x1, y1, x2, y2 = line
                painter.drawLine(x1, y1, x2, y2)

        except Exception as e:
            print(f"绘制时出错: {e}")

    def keyPressEvent(self, event):
        """处理键盘事件"""
        try:
            if event.key() == Qt.Key_Escape:
                self.close_requested.emit()
            super().keyPressEvent(event)
        except Exception as e:
            print(f"键盘事件处理出错: {e}")

    def mousePressEvent(self, event):
        """处理鼠标点击事件"""
        try:
            if event.button() == Qt.RightButton:
                self.close_requested.emit()
            super().mousePressEvent(event)
        except Exception as e:
            print(f"鼠标事件处理出错: {e}")